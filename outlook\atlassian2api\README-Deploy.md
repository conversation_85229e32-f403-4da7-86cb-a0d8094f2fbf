# OpenAI Compatible API Server - Deno Deploy

🚀 部署在 Deno Deploy 的 OpenAI 兼容 API 服务器，通过 Atlassian AI Gateway 代理访问 Claude 等 AI 模型。

## ✨ 特性

- 🔄 **OpenAI 兼容**: 完全兼容 OpenAI API 格式
- 🌊 **流式支持**: 支持流式和非流式响应
- 🔄 **自动重试**: 凭据失效时自动切换备用凭据
- 🌐 **CORS 支持**: 支持跨域请求
- ☁️ **云端部署**: 部署在 Deno Deploy，全球 CDN 加速
- 🔒 **环境变量**: 安全的凭据管理

## 🚀 快速部署

### 1. Fork 项目
1. Fork 这个仓库到你的 GitHub 账户
2. 或者下载 `main.ts` 和 `deno.json` 文件

### 2. 部署到 Deno Deploy
1. 访问 [Deno Deploy](https://dash.deno.com/)
2. 登录并创建新项目
3. **项目名称要求**：
   - 长度：3-26 个字符
   - 只能包含：a-z, 0-9, - (连字符)
   - 不能以连字符开头或结尾
   - 连字符后不能是 8 或 12 位字符
   - **推荐名称**：`openai-proxy`, `claude-api`, `ai-gateway`, `rovo-proxy`
4. 连接你的 GitHub 仓库
5. 选择 `main.ts` 作为入口文件
6. 配置环境变量（见下方）

### 3. 配置环境变量
在 Deno Deploy 项目设置中添加以下环境变量：

#### 必需变量
```
ATLASSIAN_EMAIL=<EMAIL>
ATLASSIAN_TOKEN=your-atlassian-api-token
```

#### 可选变量
```
DEBUG_MODE=false
ATLASSIAN_EMAIL_1=<EMAIL>
ATLASSIAN_TOKEN_1=backup-token-1
ATLASSIAN_EMAIL_2=<EMAIL>
ATLASSIAN_TOKEN_2=backup-token-2
```

## 📡 API 端点

### 获取模型列表
```bash
GET https://your-project.deno.dev/v1/models
```

### 聊天完成
```bash
POST https://your-project.deno.dev/v1/chat/completions
Content-Type: application/json

{
  "model": "anthropic:claude-3-5-sonnet-v2@20241022",
  "messages": [
    {"role": "user", "content": "Hello!"}
  ],
  "temperature": 0.7,
  "stream": false
}
```

### 健康检查
```bash
GET https://your-project.deno.dev/health
```

## 🤖 支持的模型

- `anthropic:claude-3-5-sonnet-v2@20241022` ⭐ (推荐)
- `anthropic:claude-3-7-sonnet@20250219`
- `anthropic:claude-sonnet-4@20250514`
- `bedrock:anthropic.claude-3-5-sonnet-20241022-v2:0`
- `bedrock:anthropic.claude-3-7-sonnet-20250219-v1:0`
- `bedrock:anthropic.claude-sonnet-4-20250514-v1:0`

## 🔧 在 Cherry Studio 中使用

1. 打开 Cherry Studio 设置
2. 添加新的 API 提供商：
   - **名称**: `Atlassian AI Gateway`
   - **Base URL**: `https://your-project.deno.dev`
   - **API Key**: 任意值（如 `sk-dummy`）
3. 选择模型开始对话

## 🛠️ 本地开发

### 克隆项目
```bash
git clone <your-repo>
cd RovoDevAgents
```

### 设置环境变量
```bash
export ATLASSIAN_EMAIL="<EMAIL>"
export ATLASSIAN_TOKEN="your-atlassian-api-token"
export DEBUG_MODE="true"
```

### 运行服务器
```bash
deno task start
# 或
deno run --allow-net --allow-env main.ts
```

### 开发模式（自动重载）
```bash
deno task dev
```

## 📊 监控和日志

### 查看部署状态
在 Deno Deploy 控制台中可以查看：
- 部署状态
- 实时日志
- 性能指标
- 错误统计

### 健康检查
```bash
curl https://your-project.deno.dev/health
```

返回示例：
```json
{
  "status": "ok",
  "timestamp": "2025-06-17T12:00:00.000Z",
  "models": 6,
  "credentials": 2
}
```

## 🔒 安全建议

1. **环境变量**: 永远不要在代码中硬编码凭据
2. **API 密钥**: 定期轮换 Atlassian API Token
3. **访问控制**: 考虑添加 API 密钥验证
4. **监控**: 定期检查使用情况和错误日志

## 🚨 故障排除

### 常见问题

#### 1. 部署失败
- 检查 `main.ts` 文件是否正确
- 确认环境变量已正确设置
- 查看 Deno Deploy 部署日志

#### 2. API 调用失败
- 检查 Atlassian 凭据是否有效
- 确认模型 ID 是否正确
- 查看服务器日志（启用 DEBUG_MODE）

#### 3. CORS 错误
- 确认请求头包含正确的 Content-Type
- 检查浏览器控制台错误信息

### 调试模式
设置环境变量 `DEBUG_MODE=true` 启用详细日志。

## 📈 性能优化

1. **多凭据**: 配置多个备用凭据提高可用性
2. **缓存**: Deno Deploy 自动提供全球 CDN 缓存
3. **监控**: 使用 Deno Deploy 内置监控功能

## 🔄 更新部署

1. 修改代码并推送到 GitHub
2. Deno Deploy 会自动重新部署
3. 或者在控制台手动触发部署

## 📞 支持

如果遇到问题：
1. 检查 Deno Deploy 日志
2. 确认环境变量配置
3. 测试 Atlassian API 凭据
4. 查看健康检查端点

## 📄 许可证

MIT License

---

🎉 现在你有了一个部署在云端的 OpenAI 兼容 API 服务器！