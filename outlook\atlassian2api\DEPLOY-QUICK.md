# 🚀 快速部署指南

## ⚠️ 重要：项目命名规范

Deno Deploy 项目名称必须符合以下规范：
- **长度**：3-26 个字符
- **字符**：只能包含 a-z, 0-9, - (连字符)
- **限制**：不能以连字符开头或结尾，连字符后不能是 8 或 12 位字符

## ✅ 推荐的项目名称

```
openai-proxy     ✅ 推荐
claude-api       ✅ 推荐  
ai-gateway       ✅ 推荐
rovo-proxy       ✅ 推荐
anthropic-api    ✅ 推荐
chat-proxy       ✅ 推荐
llm-gateway      ✅ 推荐
```

## ❌ 避免的名称

```
RovoDevAgents    ❌ 包含大写字母
rovo_proxy       ❌ 包含下划线
-openai-proxy    ❌ 以连字符开头
openai-proxy-    ❌ 以连字符结尾
api-12345678     ❌ 连字符后8位字符
proxy-123456789012  ❌ 连字符后12位字符
```

## 🎯 部署步骤

### 1. 准备文件
确保你有以下文件：
- `main.ts` - 主服务器文件
- `deno.json` - Deno 配置文件

### 2. 部署到 Deno Deploy
1. 访问 https://dash.deno.com/
2. 点击 "New Project"
3. **项目名称**：输入符合规范的名称（如 `openai-proxy`）
4. 选择部署方式：
   - **GitHub 集成**（推荐）：连接仓库，选择 `main.ts`
   - **直接上传**：上传 `main.ts` 文件

### 3. 配置环境变量
在项目设置中添加：
```
ATLASSIAN_EMAIL=<EMAIL>
ATLASSIAN_TOKEN=ATATT3xFfGF0JViaOzqL71JfmA91fg0KDgKmPUcCH1AJUM8uBjH9G9AyYIdAH7re0v9224VVr0OfqdmguJluHPpRXPC-eIDHx5jEhrVzVkSQfvI69MiNKNq8awQCDUSwzAl0Zgf08bjjW2fTlTyA2ag_GkhjAK1OMjVEIrwak9CLM7Vf0Iinhl0=EFD21949
```

### 4. 测试部署
部署完成后，你的 API 地址将是：
```
https://your-project-name.deno.dev
```

测试命令：
```bash
# 健康检查
curl https://your-project-name.deno.dev/health

# 获取模型列表
curl https://your-project-name.deno.dev/v1/models

# 测试聊天
curl -X POST https://your-project-name.deno.dev/v1/chat/completions \
  -H "Content-Type: application/json" \
  -d '{
    "model": "anthropic:claude-3-5-sonnet-v2@20241022",
    "messages": [{"role": "user", "content": "Hello!"}]
  }'
```

## 🔧 在 Cherry Studio 中配置

1. 打开 Cherry Studio 设置
2. 添加新的 API 提供商：
   - **名称**: `Atlassian AI Gateway`
   - **Base URL**: `https://your-project-name.deno.dev`
   - **API Key**: `sk-dummy` (任意值)
3. 选择模型开始对话

## 🎉 完成！

现在你有了一个部署在云端的 OpenAI 兼容 API 服务器！

如果遇到问题，请检查：
1. 项目名称是否符合规范
2. 环境变量是否正确设置
3. `main.ts` 文件是否正确上传