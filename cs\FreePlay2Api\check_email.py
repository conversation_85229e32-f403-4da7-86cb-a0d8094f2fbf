import aiohttp
import asyncio


headers = {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0",
    "Accept": "application/json",
    "Accept-Language": "en-US,en;q=0.5",
    "Referer": "https://app.freeplay.ai/signup",
    "Content-Type": "application/json"
}

async def check_email(email):
    url = "https://app.freeplay.ai/app_data/auth/signup/check-email"
    params = {
        "email": email
    }
    
    async with aiohttp.ClientSession() as session:
        async with session.get(url, headers=headers, params=params) as response:
            response_text = await response.text()
            response_json = await response.json()
            print(f"检查邮箱 {email}: {response_text}")
            return response_json.get("is_valid", False)

if __name__ == "__main__":
    asyncio.run(check_email("<EMAIL>"))