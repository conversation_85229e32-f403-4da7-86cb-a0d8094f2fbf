import aiohttp
import asyncio
import json
import random
import string
from faker import Faker
import re
import time

fake = Faker()

# 注册请求的headers
reg_headers = {
    "accept": "application/json",
    "accept-language": "zh-CN,zh;q=0.9",
    "content-type": "application/json",
    "origin": "https://app.freeplay.ai",
    "referer": "https://app.freeplay.ai/signup",
    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36"
}

# 检查请求的headers
check_headers = {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0",
    "Accept": "application/json",
    "Accept-Language": "en-US,en;q=0.5",
    "Referer": "https://app.freeplay.ai/signup",
    "Content-Type": "application/json"
}

# 获取项目的headers
project_headers = {
    "accept": "application/json",
    "accept-language": "zh-CN,zh;q=0.9",
    "content-type": "application/json",
    "referer": "https://app.freeplay.ai/projects",
    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36"
}

# 访问welcome页面的headers
welcome_headers = {
    "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
    "accept-language": "zh-CN,zh;q=0.9",
    "cache-control": "no-cache",
    "pragma": "no-cache",
    "priority": "u=0, i",
    "referer": "https://app.freeplay.ai/signup",
    "sec-ch-ua": "\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"",
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": "\"Windows\"",
    "sec-fetch-dest": "document",
    "sec-fetch-mode": "navigate",
    "sec-fetch-site": "same-origin",
    "sec-fetch-user": "?1",
    "upgrade-insecure-requests": "1",
    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36"
}

def generate_random_string(length=8):
    """生成随机字符串"""
    return ''.join(random.choices(string.ascii_lowercase + string.digits, k=length))

def generate_email():
    """生成随机邮箱"""
    domains = ['outlook.com', 'hotmail.com']
    username = generate_random_string(random.randint(6, 12))
    domain = random.choice(domains)
    return f"{username}@{domain}"

def generate_password():
    """生成随机密码"""
    # 密码包含大小写字母、数字和特殊字符，长度8-12位
    lowercase = string.ascii_lowercase
    uppercase = string.ascii_uppercase
    digits = string.digits
    special_chars = "!@#$%^&*"
    
    # 确保密码包含各种字符类型
    password = [
        random.choice(lowercase),
        random.choice(uppercase),
        random.choice(digits),
        random.choice(special_chars)
    ]
    
    # 填充剩余长度
    all_chars = lowercase + uppercase + digits + special_chars
    length = random.randint(8, 12)
    for _ in range(length - 4):
        password.append(random.choice(all_chars))
    
    # 打乱顺序
    random.shuffle(password)
    return ''.join(password)

async def check_organization(session, org_name):
    """检查组织是否可用"""
    url = "https://app.freeplay.ai/app_data/auth/signup/check-organization"
    params = {"name": org_name}
    
    try:
        async with session.get(url, headers=check_headers, params=params) as response:
            response_json = await response.json()
            return response_json.get("is_valid", False)
    except Exception as e:
        print(f"检查组织时出错: {e}")
        return False

async def check_email(session, email):
    """检查邮箱是否可用"""
    url = "https://app.freeplay.ai/app_data/auth/signup/check-email"
    params = {"email": email}
    
    try:
        async with session.get(url, headers=check_headers, params=params) as response:
            response_json = await response.json()
            return response_json.get("is_valid", False)
    except Exception as e:
        print(f"检查邮箱时出错: {e}")
        return False

async def register_user(session, email, password, account_name, first_name, last_name):
    """注册用户"""
    url = "https://app.freeplay.ai/app_data/auth/signup"
    data = {
        "email": email,
        "password": password,
        "account_name": account_name,
        "first_name": first_name,
        "last_name": last_name
    }
    
    try:
        async with session.post(url, headers=reg_headers, json=data) as response:
            response_text = await response.text()
            
            # 提取session
            set_cookie = response.headers.get('Set-Cookie', '')
            session_match = re.search(r'session=([^;]+)', set_cookie)
            session_value = session_match.group(1) if session_match else None
            
            return {
                'status': response.status,
                'response': response_text,
                'session': session_value
            }
    except Exception as e:
        print(f"注册用户时出错: {e}")
        return {'status': 0, 'response': str(e), 'session': None}

async def get_projects(session, session_value):
    """获取项目信息"""
    url = "https://app.freeplay.ai/app_data/globals"
    cookies = {"session": session_value}
    
    try:
        async with session.get(url, headers=project_headers, cookies=cookies) as response:
            if response.status == 200:
                response_json = await response.json()
                all_projects = response_json.get("all_projects", [])
                if all_projects:
                    project_id = all_projects[0]["id"]
                    return project_id
                else:
                    return None
            else:
                return None
    except Exception as e:
        print(f"获取项目时出错: {e}")
        return None

async def visit_welcome(session, session_value, project_id):
    """访问welcome页面"""
    url = f"https://app.freeplay.ai/projects/{project_id}/sessions"
    params = {
        "days_back": "all_time",
        "welcome": "true"
    }
    cookies = {"session": session_value}
    
    try:
        async with session.get(url, headers=welcome_headers, cookies=cookies, params=params) as response:
            if response.status == 200:
                print(f"✅ Welcome页面访问成功")
                return True
            else:
                print(f"⚠️ Welcome页面访问失败，状态码: {response.status}")
                return False
    except Exception as e:
        print(f"访问Welcome页面时出错: {e}")
        return False

async def register_single_account(session, account_num, total_accounts):
    """注册单个账户"""
    print(f"\n=== 开始注册第 {account_num}/{total_accounts} 个账户 ===")
    
    # 1. 生成并验证组织名
    org_attempts = 0
    max_attempts = 10
    
    while org_attempts < max_attempts:
        org_name = generate_random_string(random.randint(6, 12))
        
        if await check_organization(session, org_name):
            break
        else:
            org_attempts += 1
    else:
        print(f"❌ 第{account_num}个账户: 无法找到可用的组织名")
        return False
    
    # 2. 生成并验证邮箱
    email_attempts = 0
    
    while email_attempts < max_attempts:
        email = generate_email()
        
        if await check_email(session, email):
            break
        else:
            email_attempts += 1
    else:
        print(f"❌ 第{account_num}个账户: 无法找到可用的邮箱")
        return False
    
    # 3. 生成用户信息
    password = generate_password()
    first_name = fake.first_name()
    last_name = fake.last_name()
    
    # 4. 注册用户
    result = await register_user(session, email, password, org_name, first_name, last_name)
    
    if result['status'] == 200 and result['session']:
        # 5. 获取项目ID
        project_id = await get_projects(session, result['session'])
        
        if project_id:
            # 6. 访问welcome页面
            welcome_success = await visit_welcome(session, result['session'], project_id)
            
            # 7. 保存到文件（包含项目ID和余额）
            account_info = f"{email}----{password}----{result['session']}----{project_id}----5.00\n"
            with open('accounts.txt', 'a', encoding='utf-8') as f:
                f.write(account_info)
            
            if welcome_success:
                print(f"✅ 第{account_num}个账户注册成功并完成welcome访问: {email}")
            else:
                print(f"⚠️ 第{account_num}个账户注册成功但welcome访问失败: {email}")
            return True
        else:
            # 如果获取项目ID失败，仍然保存基本信息
            account_info = f"{email}----{password}----{result['session']}----获取项目ID失败----5.00\n"
            # with open('accounts.txt', 'a', encoding='utf-8') as f:
            #     f.write(account_info)
            
            print(f"⚠️ 第{account_num}个账户注册成功但项目ID获取失败: {email}")
            return True
    else:
        print(f"❌ 第{account_num}个账户注册失败: {email}")
        return False

async def batch_register(total_accounts=1000, concurrent_limit=5):
    """批量注册账户"""
    print(f"🚀 开始批量注册 {total_accounts} 个账户")
    print(f"📊 并发限制: {concurrent_limit} 个同时进行")
    print("=" * 50)
    
    start_time = time.time()
    success_count = 0
    failed_count = 0
    
    # 创建信号量来限制并发数
    semaphore = asyncio.Semaphore(concurrent_limit)
    
    async def register_with_semaphore(session, account_num):
        async with semaphore:
            return await register_single_account(session, account_num, total_accounts)
    
    async with aiohttp.ClientSession() as session:
        # 创建所有注册任务
        tasks = []
        for i in range(1, total_accounts + 1):
            task = register_with_semaphore(session, i)
            tasks.append(task)
        
        # 执行所有任务
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 统计结果
        for result in results:
            if isinstance(result, Exception):
                failed_count += 1
                print(f"❌ 注册过程中出现异常: {result}")
            elif result:
                success_count += 1
            else:
                failed_count += 1
    
    end_time = time.time()
    total_time = end_time - start_time
    
    # 输出统计信息
    print("\n" + "=" * 50)
    print("📊 批量注册完成统计:")
    print(f"✅ 成功注册: {success_count} 个账户")
    print(f"❌ 注册失败: {failed_count} 个账户")
    print(f"⏱️ 总耗时: {total_time:.2f} 秒")
    print(f"⚡ 平均速度: {success_count/total_time:.2f} 个/秒")
    print(f"📁 账户信息已保存到: accounts.txt")

async def auto_register():
    """自动注册流程 - 单个账户版本（保持向后兼容）"""
    await batch_register(1)

if __name__ == "__main__":
    # 可以通过修改这里的参数来控制注册数量和并发数
    # batch_register(注册数量, 并发限制)
    asyncio.run(batch_register(4, 1))  # 注册4个账户，单线程（并发数为1）