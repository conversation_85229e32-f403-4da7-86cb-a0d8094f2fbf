#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
OneRouter工具测试脚本
用于验证所有工具的基本功能
"""

import requests
import json
import sys
import os

def test_onerouter_connection():
    """测试OneRouter连接"""
    print("🌐 测试OneRouter连接...")
    
    try:
        response = requests.get("https://app.onerouter.pro/api/status", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print("✅ OneRouter连接成功")
            print(f"   系统名称: {data.get('data', {}).get('system_name', 'N/A')}")
            print(f"   GitHub OAuth: {'启用' if data.get('data', {}).get('github_oauth') else '禁用'}")
            print(f"   Google OAuth: {'启用' if data.get('data', {}).get('google_client_id') else '禁用'}")
            return True
        else:
            print(f"❌ OneRouter连接失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ OneRouter连接错误: {e}")
        return False

def test_analyzer_import():
    """测试分析器导入"""
    print("\n🔍 测试分析器导入...")
    
    try:
        from onerouter_analyzer import OneRouterAnalyzer
        analyzer = OneRouterAnalyzer()
        print("✅ 分析器导入成功")
        
        # 测试状态检查
        status = analyzer.check_status()
        if status.get('data'):
            print("✅ 状态检查功能正常")
        return True
    except Exception as e:
        print(f"❌ 分析器导入失败: {e}")
        return False

def test_batch_register_import():
    """测试批量注册器导入"""
    print("\n📝 测试批量注册器导入...")
    
    try:
        from onerouter_batch_register import OneRouterBatchRegister, TempEmailService
        register = OneRouterBatchRegister()
        temp_email = TempEmailService()
        print("✅ 批量注册器导入成功")
        
        # 测试虚假邮箱生成
        fake_email = temp_email.generate_fake_email()
        print(f"✅ 虚假邮箱生成: {fake_email['email']}")
        return True
    except Exception as e:
        print(f"❌ 批量注册器导入失败: {e}")
        return False

def test_config_files():
    """测试配置文件"""
    print("\n⚙️ 测试配置文件...")
    
    config_files = [
        "batch_config.json",
        "onerouter_analysis.json"
    ]
    
    for config_file in config_files:
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                print(f"✅ {config_file} 加载成功")
            except Exception as e:
                print(f"❌ {config_file} 加载失败: {e}")
        else:
            print(f"⚠️ {config_file} 不存在")

def test_documentation():
    """测试文档文件"""
    print("\n📚 测试文档文件...")
    
    doc_files = [
        "批量注册使用指南.md",
        "onerouter_analysis_report.md"
    ]
    
    for doc_file in doc_files:
        if os.path.exists(doc_file):
            print(f"✅ {doc_file} 存在")
        else:
            print(f"❌ {doc_file} 不存在")

def main():
    """主测试函数"""
    print("🎯 OneRouter工具集测试")
    print("=" * 50)
    
    # 运行所有测试
    tests = [
        test_onerouter_connection,
        test_analyzer_import,
        test_batch_register_import,
        test_config_files,
        test_documentation
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ 测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！工具集可以正常使用")
    else:
        print("⚠️ 部分测试失败，请检查相关组件")
    
    print("\n🚀 可用的工具:")
    print("1. python onerouter_analyzer.py - API分析")
    print("2. python onerouter_manual_guide.py - 手动指南")
    print("3. python onerouter_batch_register.py - 批量注册")
    print("4. python onerouter_automation.py - 自动化登录")

if __name__ == "__main__":
    main()