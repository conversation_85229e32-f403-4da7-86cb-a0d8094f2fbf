/**
 * API模块 - 处理与后端的通信
 */

class MusicAPI {
    constructor() {
        this.baseURL = '';
        this.timeout = 30000; // 30秒超时
    }

    /**
     * 通用请求方法
     */
    async request(endpoint, options = {}) {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), this.timeout);

        try {
            const fetchOptions = {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    ...options.headers
                },
                body: JSON.stringify(options.body || {}),
                signal: controller.signal
            };

            console.log('🌐 发送请求:', endpoint, fetchOptions);

            const response = await fetch(`${this.baseURL}${endpoint}`, fetchOptions);

            clearTimeout(timeoutId);

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            return await response.json();
        } catch (error) {
            clearTimeout(timeoutId);
            if (error.name === 'AbortError') {
                throw new Error('请求超时，请检查网络连接');
            }
            throw error;
        }
    }

    /**
     * 音质映射
     */
    mapQuality(quality) {
        const qualityMap = {
            'master': 'jymaster',  // 母带音质映射到jymaster
            'hires': 'hires',      // Hi-Res音质
            'lossless': 'lossless' // 无损音质
        };
        return qualityMap[quality] || quality;
    }

    /**
     * 解析单曲
     */
    async parseSong(songId, quality = 'lossless') {
        try {
            const mappedQuality = this.mapQuality(quality);
            console.log(`🎵 解析单曲: ${songId}, 音质: ${quality} -> ${mappedQuality}`);

            const data = await this.request('/api/song', {
                body: {
                    ids: songId,
                    level: mappedQuality
                }
            });

            if (data.status === 200) {
                return {
                    success: true,
                    data: data
                };
            } else {
                return {
                    success: false,
                    error: data.error || '解析失败'
                };
            }
        } catch (error) {
            console.error('解析单曲失败:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * 获取歌单/专辑信息
     */
    async getPlaylistInfo(type, id) {
        try {
            console.log(`📋 获取${type}信息: ${id}`);
            
            const data = await this.request('/api/playlist-info', {
                body: {
                    type: type,
                    id: id
                }
            });

            if (data.status === 200) {
                return {
                    success: true,
                    data: data
                };
            } else {
                return {
                    success: false,
                    error: data.error || '获取信息失败'
                };
            }
        } catch (error) {
            console.error('获取歌单信息失败:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * 检查Cookie状态
     */
    async checkCookieStatus() {
        try {
            console.log('🍪 检查Cookie状态');

            const data = await this.request('/api/cookie-check', {
                body: {}
            });

            if (data.status === 200) {
                return {
                    success: true,
                    data: data
                };
            } else {
                return {
                    success: false,
                    error: data.error || '检查失败'
                };
            }
        } catch (error) {
            console.error('Cookie状态检查失败:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * 批量下载歌曲
     */
    async batchDownload(songs, quality = 'lossless') {
        const results = [];
        let successCount = 0;
        let failCount = 0;

        for (let i = 0; i < songs.length; i++) {
            const song = songs[i];
            
            try {
                // 更新进度回调
                if (this.onProgress) {
                    this.onProgress({
                        current: i + 1,
                        total: songs.length,
                        song: song,
                        success: successCount,
                        failed: failCount
                    });
                }

                const result = await this.parseSong(song.id, quality);
                
                if (result.success && result.data.url) {
                    // 下载文件
                    await this.downloadFile(result.data.url, `${song.artist} - ${song.name}`, quality);
                    successCount++;
                    results.push({
                        ...song,
                        status: 'success',
                        quality: result.data.level
                    });
                } else {
                    failCount++;
                    results.push({
                        ...song,
                        status: 'failed',
                        error: result.error
                    });
                }

                // 添加延迟避免请求过快
                await new Promise(resolve => setTimeout(resolve, 500));

            } catch (error) {
                failCount++;
                results.push({
                    ...song,
                    status: 'failed',
                    error: error.message
                });
            }
        }

        return {
            results,
            successCount,
            failCount
        };
    }

    /**
     * 下载文件
     */
    async downloadFile(url, filename, quality) {
        try {
            const response = await fetch(url);
            const blob = await response.blob();
            
            // 确定文件扩展名
            const extension = (quality === 'lossless' || quality === 'hires' || quality === 'master') ? 'flac' : 'mp3';
            const fullFilename = `${filename}.${extension}`;
            
            // 创建下载链接
            const downloadUrl = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = downloadUrl;
            link.download = fullFilename;
            link.style.display = 'none';
            
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            
            // 释放URL对象
            setTimeout(() => window.URL.revokeObjectURL(downloadUrl), 100);
            
            return true;
        } catch (error) {
            console.error('下载文件失败:', error);
            throw error;
        }
    }

    /**
     * 设置进度回调
     */
    setProgressCallback(callback) {
        this.onProgress = callback;
    }
}

// 创建全局API实例
window.musicAPI = new MusicAPI();
