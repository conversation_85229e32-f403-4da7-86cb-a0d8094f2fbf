# OneRouter API 分析报告

## 🎯 项目概述

OneRouter (https://app.onerouter.pro) 是一个大语言模型聚合器平台，用户可以通过该平台访问第三方API来使用各种生成式AI模型。

## 📊 系统状态

- ✅ **系统在线**: 服务正常运行
- ✅ **GitHub OAuth**: 已启用
- ✅ **Google OAuth**: 已启用  
- ✅ **邮箱验证**: 不需要
- 🏢 **系统名称**: OneRouter
- 💰 **支付方式**: Stripe (美元)
- 📧 **联系邮箱**: <EMAIL>

## 🔐 OAuth 配置信息

### GitHub OAuth
- **Client ID**: `Ov23liOluRxWNZLIiX6k`
- **授权范围**: `user:email`
- **授权URL**: `https://github.com/login/oauth/authorize`
- **回调地址**: `https://app.onerouter.pro/api/auth/github/callback`

### Google OAuth  
- **Client ID**: `623402972213-jaoasddet9ji3s8ki94qg1q3sbn5qpoc.apps.googleusercontent.com`
- **重定向URL**: `https://app.onerouter.pro/login`

## 🔍 已发现的API端点

### 公开端点
- ✅ `GET /api/status` - 系统状态信息
- ✅ `POST /api/user/login` - 用户登录
- ✅ `GET /api/user/logout` - 用户登出

### 需要认证的端点
- 🔒 `GET /api/user/profile` - 用户资料 (401)
- 🔒 `GET /api/user/info` - 用户信息 (401)  
- 🔒 `GET /api/user/register` - 用户注册 (401)
- 🔒 `GET /api/models` - 可用模型列表 (401)

### 未找到的端点 (404)
- ❌ `/api/keys` - API密钥管理
- ❌ `/api/keys/generate` - 生成API密钥
- ❌ `/api/usage` - 使用统计
- ❌ `/api/billing` - 账单信息
- ❌ `/api/chat/completions` - 聊天完成
- ❌ `/api/v1/chat/completions` - OpenAI兼容接口

## 💡 获取API访问权限的方法

### 方法一：GitHub OAuth 登录

1. **访问登录页面**: https://app.onerouter.pro/login
2. **点击GitHub登录按钮**
3. **GitHub授权URL构造**:
   ```
   https://github.com/login/oauth/authorize?client_id=Ov23liOluRxWNZLIiX6k&scope=user:email&redirect_uri=https://app.onerouter.pro/api/auth/github/callback
   ```
4. **完成GitHub授权**
5. **获取session cookies**:
   - 打开浏览器开发者工具 (F12)
   - 查看Network标签页的请求
   - 复制Cookie头中的session值

### 方法二：Google OAuth 登录

1. **点击Google登录按钮**
2. **使用Google账号授权**
3. **获取session cookies**

### 方法三：邮箱密码注册 (如果支持)

1. **使用临时邮箱服务**:
   - https://temp-mail.org
   - https://10minutemail.com
   - https://guerrillamail.com
2. **注册新账号**
3. **登录获取session**

## 🔑 API密钥获取策略

由于标准的API密钥端点返回404，可能的获取方式：

### 可能的端点位置
- `/api/user/keys`
- `/api/user/tokens` 
- `/api/auth/tokens`
- `/dashboard/api-keys`
- `/settings/api-keys`

### 前端页面查找
1. 登录后访问用户仪表板
2. 查找"API Keys"、"Tokens"、"Settings"等页面
3. 通过浏览器开发者工具监控网络请求

## 🧪 测试方法

### 使用cURL测试认证端点

```bash
# 测试用户信息
curl -X GET 'https://app.onerouter.pro/api/user/info' \
  -H 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36' \
  -H 'Accept: application/json' \
  -H 'Cookie: session=YOUR_SESSION_COOKIE' \
  -H 'Referer: https://app.onerouter.pro/dashboard'

# 测试模型列表
curl -X GET 'https://app.onerouter.pro/api/models' \
  -H 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36' \
  -H 'Accept: application/json' \
  -H 'Cookie: session=YOUR_SESSION_COOKIE' \
  -H 'Referer: https://app.onerouter.pro/dashboard'
```

## 📋 业务模式分析

### 服务特点
- **模型聚合器**: 提供多种AI模型的统一访问接口
- **预付费积分**: 用户需要购买积分来使用服务
- **积分过期**: 365天后未使用的积分会过期
- **24小时退款**: 购买后24小时内可申请退款

### 定价信息
- **配额单位**: 1,000,000 (可能是tokens)
- **支付处理**: 通过Stripe处理美元支付
- **货币显示**: 支持货币格式显示

## 🚨 注意事项

### 合规性
- 遵守OneRouter的服务条款
- 不进行恶意自动化攻击
- 尊重速率限制

### 技术限制
- 某些端点可能需要特定的认证头
- API密钥生成可能需要付费账户
- 部分功能可能仅对特定用户开放

### 安全建议
- 使用HTTPS进行所有API调用
- 妥善保管session cookies和API密钥
- 定期轮换访问凭证

## 🔧 自动化工具

本分析提供了以下工具：

1. **onerouter_analyzer.py** - API端点发现和分析
2. **onerouter_automation.py** - GitHub OAuth自动化登录
3. **onerouter_manual_guide.py** - 手动获取指南和测试工具

## 📞 联系方式

如需技术支持或商业合作：
- 📧 邮箱: <EMAIL>
- 🏢 地址: 49 Powell St, San Francisco, CA 94102, US

---

*报告生成时间: 2025年6月16日*
*分析工具版本: v1.0*