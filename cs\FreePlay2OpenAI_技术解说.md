# FreePlay2OpenAI 转换服务技术解说

## 🎯 项目概述

这是一个将FreePlay AI的API转换为OpenAI兼容格式的中间件服务，让用户可以在支持OpenAI API的客户端（如Cherry Studio）中使用FreePlay的Claude模型。

## 🏗️ 系统架构

```
[客户端] → [转换服务器] → [FreePlay API]
Cherry Studio  py.py服务器   app.freeplay.ai
```

### 核心组件
1. **注册机** (`auto_register.py`) - 自动批量注册FreePlay账户
2. **转换服务器** (`py.py`) - OpenAI API兼容层
3. **账户池管理** - 自动账户切换和余额监控

## 🔧 技术实现详解

### 1. 注册机模块 (`auto_register.py`)

#### 🎲 随机数据生成
```python
def generate_email():
    """生成随机邮箱"""
    domains = ['outlook.com', 'hotmail.com']
    username = generate_random_string(random.randint(6, 12))
    domain = random.choice(domains)
    return f"{username}@{domain}"

def generate_password():
    """生成符合要求的随机密码"""
    # 包含大小写字母、数字、特殊字符
    lowercase = string.ascii_lowercase
    uppercase = string.ascii_uppercase
    digits = string.digits
    special_chars = "!@#$%^&*"
```

#### 🔍 账户验证流程
```python
async def check_organization(session, org_name):
    """检查组织名是否可用"""
    url = "https://app.freeplay.ai/app_data/auth/signup/check-organization"
    params = {"name": org_name}
    # 发送GET请求验证组织名

async def check_email(session, email):
    """检查邮箱是否可用"""
    url = "https://app.freeplay.ai/app_data/auth/signup/check-email"
    params = {"email": email}
    # 发送GET请求验证邮箱
```

#### 📝 注册流程
```python
async def register_user(session, email, password, account_name, first_name, last_name):
    """注册用户账户"""
    url = "https://app.freeplay.ai/app_data/auth/signup"
    data = {
        "email": email,
        "password": password,
        "account_name": account_name,
        "first_name": first_name,
        "last_name": last_name
    }
    # 发送POST请求注册账户
    # 从响应头提取session cookie
```

#### 🎯 项目ID获取
```python
async def get_projects(session, session_value):
    """获取用户项目信息"""
    url = "https://app.freeplay.ai/app_data/globals"
    cookies = {"session": session_value}
    # 获取用户的项目列表，提取第一个项目的ID
```

#### 🚀 并发处理
```python
async def batch_register(total_accounts=1000, concurrent_limit=5):
    """批量注册，支持并发控制"""
    semaphore = asyncio.Semaphore(concurrent_limit)
    
    async def register_with_semaphore(session, account_num):
        async with semaphore:
            return await register_single_account(session, account_num, total_accounts)
    
    # 创建并发任务
    tasks = []
    for i in range(1, total_accounts + 1):
        task = register_with_semaphore(session, i)
        tasks.append(task)
    
    # 执行所有任务
    results = await asyncio.gather(*tasks, return_exceptions=True)
```

### 2. 转换服务器 (`py.py`)

#### 🗂️ 账户池管理
```python
class AccountPool:
    def __init__(self, accounts_file="accounts.txt"):
        self.accounts_file = accounts_file
        self.accounts = []
        self.current_index = 0  # 当前账号索引
        self.load_accounts()
    
    def get_current_account(self):
        """获取当前可用账号，自动切换余额不足的账号"""
        # 检查当前账号余额
        # 如果不足，自动切换到下一个有余额的账号
        # 实时更新账号余额
```

#### 🔄 API转换核心
```python
def call_freeplay_api_with_retry(messages, stream=False, model="claude-3-7-sonnet-********", max_retries=None):
    """调用FreePlay API，支持自动重试不同账号"""
    
    # 1. 验证模型配置
    model_config = MODEL_MAPPING[model]
    
    # 2. 循环尝试不同账号
    for retry_count in range(max_retries):
        account = account_pool.get_current_account()
        
        # 3. 构建FreePlay API请求
        url = f"https://app.freeplay.ai/app_data/projects/{account['project_id']}/llm-completions"
        
        json_data = {
            "messages": messages,
            "params": [
                {"name": "max_tokens", "value": model_config['max_tokens']},
                {"name": "temperature", "value": 0.08},
                {"name": "top_p", "value": 0.14},
                {"name": "top_k", "value": 1}
            ],
            "model_id": model_config['model_id'],
            "variables": {},
            "history": None,
            "asset_references": {}
        }
        
        # 4. 发送请求到FreePlay
        response = requests.post(url, headers=headers, cookies=cookies, files=files, stream=True)
        
        # 5. 处理响应或切换账号
```

#### 🌊 流式响应转换
```python
def generate_openai_stream_response(messages, model="claude-3-7-sonnet-********"):
    """将FreePlay的流式响应转换为OpenAI格式"""
    
    response, account = call_freeplay_api_with_retry(messages, stream=True, model=model)
    chat_id = f"chatcmpl-{uuid.uuid4().hex[:29]}"
    created = int(time.time())
    
    # 发送开始chunk
    start_chunk = {
        "id": chat_id,
        "object": "chat.completion.chunk",
        "created": created,
        "model": model,
        "choices": [{
            "index": 0,
            "delta": {"role": "assistant", "content": ""},
            "finish_reason": None
        }]
    }
    yield f"data: {json.dumps(start_chunk)}\n\n"
    
    # 处理FreePlay的流式数据
    for line in response.iter_lines(decode_unicode=True):
        if line and line.startswith('data: '):
            freeplay_data = json.loads(line[6:])
            
            # 转换为OpenAI格式
            if freeplay_data.get('content'):
                chunk = {
                    "id": chat_id,
                    "object": "chat.completion.chunk",
                    "created": created,
                    "model": model,
                    "choices": [{
                        "index": 0,
                        "delta": {"content": freeplay_data['content']},
                        "finish_reason": None
                    }]
                }
                yield f"data: {json.dumps(chunk)}\n\n"
```

#### 🛣️ Flask路由设计
```python
@app.route('/v1/chat/completions', methods=['POST'])
def chat_completions():
    """OpenAI兼容的聊天完成API"""
    data = request.get_json()
    messages = data.get('messages', [])
    stream = data.get('stream', False)
    model = data.get('model', 'claude-3-7-sonnet-********')
    
    if stream:
        return Response(
            generate_openai_stream_response(messages, model),
            mimetype='text/event-stream'
        )
    else:
        response_data = generate_openai_non_stream_response(messages, model)
        return jsonify(response_data)

@app.route('/v1/models', methods=['GET'])
def list_models():
    """列出支持的模型"""
    # 返回OpenAI格式的模型列表

@app.route('/accounts/status', methods=['GET'])
def accounts_status():
    """查看账号池状态"""
    # 返回账户统计信息
```

## 🔍 关键技术点

### 1. 协议转换
- **输入**: OpenAI格式的请求 (`/v1/chat/completions`)
- **处理**: 转换为FreePlay API格式
- **输出**: OpenAI格式的响应

### 2. 会话管理
```python
cookies = {"session": account['session_id']}
headers = {
    "accept": "*/*",
    "origin": "https://app.freeplay.ai",
    "user-agent": "Mozilla/5.0 ..."
}
```

### 3. 模型映射
```python
MODEL_MAPPING = {
    "claude-3-7-sonnet-********": {
        "model_id": "be71f37b-1487-49fa-a989-a9bb99c0b129", 
        "max_tokens": 64000
    },
    "claude-4-opus-********": {
        "model_id": "bebc7dd5-a24d-4147-85b0-8f62902ea1a3",
        "max_tokens": 32000
    }
}
```

### 4. 错误处理和重试
```python
# 账号失效时自动禁用
if response.status_code in [401, 404]:
    old_balance = account['balance']
    account['balance'] = 0.0
    account_pool.update_balance(account['session_id'], 0.0)
    account_pool.save_accounts()
    continue  # 尝试下一个账号
```

### 5. 余额监控
```python
def get_account_balance(session_id):
    """实时获取账号余额"""
    response = requests.get(
        "https://app.freeplay.ai/app_data/settings/billing",
        headers=headers,
        cookies={"session": session_id}
    )
    
    # 解析Freeplay credits使用情况
    for feature in data.get('feature_usage', []):
        if feature.get('feature_name') == 'Freeplay credits':
            usage_limit = feature.get('usage_limit', 0)
            usage_value = feature.get('usage_value', 0)
            remaining_balance = usage_limit - usage_value
            return remaining_balance, True
```

## 🎨 设计模式

### 1. 适配器模式 (Adapter Pattern)
- 将FreePlay API适配为OpenAI API格式
- 客户端无需修改，透明使用

### 2. 对象池模式 (Object Pool Pattern)
- 管理账户池，复用账户资源
- 自动负载均衡和故障转移

### 3. 策略模式 (Strategy Pattern)
- 支持不同的模型策略
- 可扩展新的模型类型

## 🚀 部署和运行

### 环境要求
```bash
pip install Flask==2.3.3 requests==2.31.0 aiohttp==3.9.1 faker==22.0.0
```

### 启动服务
```bash
python py.py
# 服务运行在 http://localhost:8000
```

### 账户文件格式
```
email----password----session_id----project_id----balance
```

## 🔒 安全考虑

1. **会话管理**: 使用FreePlay的session cookie进行认证
2. **请求头伪装**: 模拟真实浏览器请求
3. **速率限制**: 通过并发控制避免触发反爬机制
4. **错误处理**: 优雅处理账户失效和网络异常

## 📈 性能优化

1. **异步处理**: 使用aiohttp进行并发注册
2. **连接复用**: 复用HTTP连接减少开销
3. **智能重试**: 失败时自动切换账户
4. **缓存机制**: 缓存账户状态减少API调用

这个转换服务的核心思想是作为一个"翻译器"，将不同API之间的协议差异抹平，让用户可以用统一的OpenAI格式访问各种AI服务。