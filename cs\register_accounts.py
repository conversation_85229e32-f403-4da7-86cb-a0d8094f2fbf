import requests
import time

headers = {'Authorization': 'Bearer sk-freeplay-50ed7f046a384ab5b5c1ac84ce5b8b1f'}

def check_status():
    response = requests.get('http://localhost:8000/admin/accounts/status', headers=headers)
    if response.status_code == 200:
        data = response.json()
        print(f'Total accounts: {data["total_accounts"]}')
        print(f'Healthy accounts: {data["healthy_accounts"]}')
        print(f'Total balance: {data["total_balance"]}')
        return data["healthy_accounts"]
    else:
        print(f'Failed to get status: {response.status_code}')
        return 0

def register_account():
    response = requests.post('http://localhost:8000/admin/register/manual', headers=headers)
    if response.status_code == 200:
        result = response.json()
        if result.get('success'):
            account = result["account"]
            print(f'✅ Registered: {account["email"]} - {account["balance"]}')
            return True
        else:
            print(f'❌ Registration failed: {result.get("message")}')
            return False
    else:
        print(f'❌ HTTP {response.status_code}: {response.text}')
        return False

print("Current status:")
healthy_count = check_status()

print(f"\nNeed to register accounts. Current healthy: {healthy_count}")
print("Registering 3 new accounts...")

for i in range(3):
    print(f"\nRegistering account {i+1}...")
    success = register_account()
    if success:
        time.sleep(1)  # 等待1秒
    else:
        print("Registration failed, stopping...")
        break

print("\nFinal status:")
check_status()