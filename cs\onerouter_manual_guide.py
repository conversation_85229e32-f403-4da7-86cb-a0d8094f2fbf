import requests
import json
import time
from typing import Dict, Optional

class OneRouterManualGuide:
    def __init__(self):
        self.base_url = "https://app.onerouter.pro"
        
        # 从分析中获取的关键信息
        self.oauth_info = {
            "github": {
                "client_id": "Ov23liOluRxWNZLIiX6k",
                "authorize_url": "https://github.com/login/oauth/authorize",
                "scope": "user:email",
                "redirect_uri": "https://app.onerouter.pro/api/auth/github/callback"
            },
            "google": {
                "client_id": "************-jaoasddet9ji3s8ki94qg1q3sbn5qpoc.apps.googleusercontent.com",
                "redirect_url": "https://app.onerouter.pro/login"
            }
        }
        
        self.api_endpoints = {
            "status": "/api/status",
            "login": "/api/user/login", 
            "logout": "/api/user/logout",
            "profile": "/api/user/profile",
            "user_info": "/api/user/info",
            "models": "/api/models"
        }
    
    def print_manual_steps(self):
        """打印手动获取API的步骤"""
        print("🎯 OneRouter API 手动获取指南")
        print("=" * 60)
        
        print("\n📋 方法一：GitHub OAuth 手动流程")
        print("-" * 40)
        print("1. 访问 OneRouter 登录页面:")
        print(f"   {self.base_url}/login")
        
        print("\n2. 点击 'Sign in with GitHub' 按钮")
        print("   这会重定向到 GitHub OAuth 授权页面")
        
        print("\n3. GitHub OAuth URL 构造:")
        github_auth_url = (
            f"{self.oauth_info['github']['authorize_url']}"
            f"?client_id={self.oauth_info['github']['client_id']}"
            f"&scope={self.oauth_info['github']['scope']}"
            f"&redirect_uri={self.oauth_info['github']['redirect_uri']}"
        )
        print(f"   {github_auth_url}")
        
        print("\n4. 授权后会回调到:")
        print(f"   {self.oauth_info['github']['redirect_uri']}")
        
        print("\n5. 登录成功后，使用浏览器开发者工具:")
        print("   - 打开 F12 开发者工具")
        print("   - 进入 Network 标签页")
        print("   - 查看请求头中的 Cookie")
        print("   - 复制 session 相关的 cookie 值")
        
        print("\n📋 方法二：Google OAuth 手动流程")
        print("-" * 40)
        print("1. 点击 'Sign in with Google' 按钮")
        print(f"2. Google Client ID: {self.oauth_info['google']['client_id']}")
        print(f"3. 重定向URL: {self.oauth_info['google']['redirect_url']}")
        
        print("\n📋 方法三：邮箱密码注册")
        print("-" * 40)
        print("1. 访问注册页面 (如果有的话)")
        print("2. 使用临时邮箱服务:")
        print("   - https://temp-mail.org")
        print("   - https://10minutemail.com")
        print("   - https://guerrillamail.com")
        
        print("\n🔍 已发现的API端点")
        print("-" * 40)
        for name, endpoint in self.api_endpoints.items():
            print(f"   {name}: {self.base_url}{endpoint}")
        
        print("\n🔑 获取API密钥的可能位置")
        print("-" * 40)
        potential_key_endpoints = [
            "/api/keys",
            "/api/keys/generate", 
            "/api/user/keys",
            "/api/user/tokens",
            "/api/auth/tokens",
            "/dashboard/api-keys",
            "/settings/api-keys"
        ]
        for endpoint in potential_key_endpoints:
            print(f"   {self.base_url}{endpoint}")
        
        print("\n📊 系统信息")
        print("-" * 40)
        print(f"   服务器地址: https://onerouter.pro")
        print(f"   配额单位: 1,000,000")
        print(f"   支付处理: Stripe (美元)")
        print(f"   联系邮箱: <EMAIL>")
    
    def test_authenticated_access(self, cookies_str: str):
        """测试使用cookies访问认证端点"""
        print("\n🧪 测试认证访问")
        print("-" * 40)
        
        # 解析cookies字符串
        cookies = {}
        if cookies_str:
            for cookie in cookies_str.split(';'):
                if '=' in cookie:
                    key, value = cookie.strip().split('=', 1)
                    cookies[key] = value
        
        session = requests.Session()
        session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'application/json',
            'Referer': f'{self.base_url}/dashboard'
        })
        
        # 设置cookies
        for key, value in cookies.items():
            session.cookies.set(key, value)
        
        # 测试各个端点
        for name, endpoint in self.api_endpoints.items():
            try:
                response = session.get(f"{self.base_url}{endpoint}")
                print(f"   {name}: {response.status_code}")
                
                if response.status_code == 200:
                    try:
                        data = response.json()
                        if name == "models" and isinstance(data, dict) and 'data' in data:
                            print(f"      模型数量: {len(data.get('data', []))}")
                        elif name == "user_info":
                            print(f"      用户信息: {data}")
                    except:
                        print(f"      响应长度: {len(response.text)}")
                        
            except Exception as e:
                print(f"   {name}: 错误 - {e}")
    
    def generate_curl_commands(self, cookies_str: str = ""):
        """生成用于测试的curl命令"""
        print("\n🔧 cURL 测试命令")
        print("-" * 40)
        
        cookie_header = f'-H "Cookie: {cookies_str}"' if cookies_str else ""
        
        for name, endpoint in self.api_endpoints.items():
            print(f"\n# 测试 {name}")
            print(f"curl -X GET '{self.base_url}{endpoint}' \\")
            print(f"  -H 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36' \\")
            print(f"  -H 'Accept: application/json' \\")
            if cookie_header:
                print(f"  {cookie_header} \\")
            print(f"  -H 'Referer: {self.base_url}/dashboard'")
    
    def check_system_status(self):
        """检查系统状态"""
        print("\n📊 系统状态检查")
        print("-" * 40)
        
        try:
            response = requests.get(f"{self.base_url}/api/status")
            if response.status_code == 200:
                data = response.json()
                print("✅ 系统在线")
                print(f"   GitHub OAuth: {'启用' if data.get('data', {}).get('github_oauth') else '禁用'}")
                print(f"   Google OAuth: {'启用' if data.get('data', {}).get('google_client_id') else '禁用'}")
                print(f"   邮箱验证: {'需要' if data.get('data', {}).get('email_verification') else '不需要'}")
                print(f"   系统名称: {data.get('data', {}).get('system_name', 'N/A')}")
            else:
                print(f"❌ 系统状态检查失败: {response.status_code}")
        except Exception as e:
            print(f"❌ 连接失败: {e}")

def main():
    guide = OneRouterManualGuide()
    
    while True:
        print("\n" + "=" * 60)
        print("🎯 OneRouter API 获取工具")
        print("=" * 60)
        print("1. 显示手动获取指南")
        print("2. 检查系统状态") 
        print("3. 测试认证访问 (需要cookies)")
        print("4. 生成cURL测试命令")
        print("5. 退出")
        
        choice = input("\n请选择操作 (1-5): ").strip()
        
        if choice == "1":
            guide.print_manual_steps()
            
        elif choice == "2":
            guide.check_system_status()
            
        elif choice == "3":
            print("\n请从浏览器开发者工具中复制cookies:")
            print("格式: session=xxx; other_cookie=yyy")
            cookies = input("Cookies: ").strip()
            if cookies:
                guide.test_authenticated_access(cookies)
            else:
                print("❌ 未提供cookies")
                
        elif choice == "4":
            cookies = input("Cookies (可选): ").strip()
            guide.generate_curl_commands(cookies)
            
        elif choice == "5":
            print("👋 再见!")
            break
            
        else:
            print("❌ 无效选择")
        
        input("\n按回车键继续...")

if __name__ == "__main__":
    main()