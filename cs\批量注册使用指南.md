# OneRouter 批量注册使用指南

## 🎯 概述

本工具集提供了多种方式来批量获取OneRouter平台的API访问权限，包括GitHub OAuth自动化、临时邮箱注册等方法。

## 📁 文件说明

### 核心工具
- **`onerouter_batch_register.py`** - 主要的批量注册工具
- **`batch_config.json`** - 配置文件（GitHub账号、代理设置等）
- **`onerouter_analyzer.py`** - API端点分析工具
- **`onerouter_manual_guide.py`** - 手动获取指南

### 分析报告
- **`onerouter_analysis_report.md`** - 完整的技术分析报告
- **`onerouter_analysis.json`** - 详细的API分析数据

## 🚀 快速开始

### 1. 环境准备

```bash
# 安装依赖
pip install requests selenium

# 下载Chrome WebDriver
# 确保ChromeDriver在PATH中或与脚本同目录
```

### 2. 配置设置

编辑 `batch_config.json` 文件：

```json
{
  "github_accounts": [
    {
      "username": "你的GitHub用户名",
      "password": "你的GitHub密码",
      "note": "主账号"
    }
  ]
}
```

### 3. 运行批量注册

```bash
python onerouter_batch_register.py
```

## 📋 注册方法详解

### 方法一：GitHub OAuth 批量注册 ⭐推荐

**优势：**
- 成功率高
- 无需邮箱验证
- 自动化程度高

**步骤：**
1. 准备多个GitHub账号
2. 配置 `batch_config.json`
3. 运行批量注册工具
4. 自动获取session cookies

**示例代码：**
```python
register = OneRouterBatchRegister()
results = register.batch_register_github(count=5, use_proxy=True)
```

### 方法二：临时邮箱注册

**优势：**
- 不需要真实账号
- 可大量生成
- 成本低

**支持的临时邮箱服务：**
- Guerrilla Mail
- 10 Minute Mail
- 虚假邮箱生成

**示例代码：**
```python
results = register.batch_register_temp_email(count=10)
```

### 方法三：手动获取（备用方案）

使用 `onerouter_manual_guide.py` 获取详细的手动操作步骤。

## 🔧 高级配置

### 代理设置

```json
{
  "proxy_settings": {
    "enabled": true,
    "proxies": [
      "http://proxy1:8080",
      "socks5://proxy2:1080"
    ],
    "rotation": true
  }
}
```

### 并发控制

```json
{
  "registration_settings": {
    "delay_min": 10,
    "delay_max": 30,
    "concurrent_limit": 3,
    "max_retries": 3
  }
}
```

## 📊 使用示例

### 基础批量注册

```python
from onerouter_batch_register import OneRouterBatchRegister

# 初始化注册器
register = OneRouterBatchRegister()

# 加载配置
register.load_config("batch_config.json")

# 批量注册5个GitHub账号
results = register.batch_register_github(count=5)

# 保存结果
register.save_accounts("my_accounts.json")

print(f"成功注册 {len(results)} 个账号")
```

### 高级用法

```python
# 使用代理进行注册
results = register.batch_register_github(count=3, use_proxy=True)

# 临时邮箱注册
email_results = register.batch_register_temp_email(count=5)

# 获取用户信息
for account in results:
    user_info = register.get_user_info_with_cookies(account['cookies'])
    print(f"用户信息: {user_info}")
```

## 🔍 API使用

注册成功后，可以使用获取的cookies访问OneRouter API：

```python
import requests

# 使用cookies访问API
session = requests.Session()
for name, value in account['cookies'].items():
    session.cookies.set(name, value)

# 获取可用模型
response = session.get("https://app.onerouter.pro/api/models")
models = response.json()

# 获取用户信息
response = session.get("https://app.onerouter.pro/api/user/info")
user_info = response.json()
```

## ⚠️ 注意事项

### 安全建议
1. **保护账号信息** - 不要在公共场所运行或分享配置文件
2. **合理使用频率** - 避免过于频繁的注册请求
3. **遵守服务条款** - 确保使用符合OneRouter的服务条款

### 技术限制
1. **GitHub 2FA** - 启用了两步验证的GitHub账号无法自动登录
2. **IP限制** - 可能存在IP频率限制，建议使用代理
3. **验证码** - 某些情况下可能出现验证码，需要手动处理

### 法律合规
1. **仅用于学习** - 本工具仅供技术学习和研究使用
2. **遵守法律** - 使用时请遵守当地法律法规
3. **商业使用** - 商业用途请先咨询相关法律意见

## 🛠️ 故障排除

### 常见问题

**Q: Chrome WebDriver 错误**
```
A: 确保安装了正确版本的ChromeDriver，并且在PATH中可访问
```

**Q: GitHub登录失败**
```
A: 检查用户名密码是否正确，是否启用了2FA
```

**Q: 代理连接失败**
```
A: 验证代理地址和端口是否正确，代理是否可用
```

**Q: 注册成功但无法获取API**
```
A: 检查cookies是否正确设置，尝试手动访问用户仪表板
```

### 调试模式

```python
# 启用详细日志
import logging
logging.basicConfig(level=logging.DEBUG)

# 非无头模式（显示浏览器）
register.setup_driver(headless=False)
```

## 📈 性能优化

### 提高成功率
1. 使用高质量代理
2. 设置合理的延迟时间
3. 分批次执行，避免并发过高

### 资源管理
1. 及时清理临时文件
2. 合理设置超时时间
3. 监控内存使用情况

## 📞 技术支持

如遇到技术问题，可以：
1. 查看详细的错误日志
2. 检查网络连接和代理设置
3. 验证GitHub账号状态
4. 尝试手动注册流程确认问题

---

**免责声明：** 本工具仅供学习和研究使用，使用者需自行承担使用风险，并遵守相关法律法规和服务条款。