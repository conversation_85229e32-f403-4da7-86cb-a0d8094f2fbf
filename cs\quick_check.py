import requests

headers = {'Authorization': 'Bearer sk-freeplay-50ed7f046a384ab5b5c1ac84ce5b8b1f'}

try:
    response = requests.get('http://localhost:8000/admin/accounts/status', headers=headers, timeout=10)
    if response.status_code == 200:
        data = response.json()
        print(f"总账户: {data['total_accounts']}")
        print(f"健康账户: {data['healthy_accounts']}")
        print(f"总余额: {data['total_balance']}")
        
        # 检查有多少账户余额>$2.00
        healthy_count = 0
        for acc in data['accounts']:
            balance = float(acc['balance'].replace('$', ''))
            if balance > 2.0:
                healthy_count += 1
        print(f"余额>$2.00的账户: {healthy_count}")
    else:
        print(f"错误: {response.status_code}")
except Exception as e:
    print(f"异常: {e}")