import requests
import json
import time
import uuid
import logging
from typing import Dict, Optional, List
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
import tempfile
import os

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class OneRouterAutomation:
    def __init__(self):
        self.base_url = "https://app.onerouter.pro"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Origin': 'https://app.onerouter.pro',
            'Referer': 'https://app.onerouter.pro/login',
        })
        
        # 从分析结果中获取的关键信息
        self.github_client_id = "Ov23liOluRxWNZLIiX6k"
        self.google_client_id = "623402972213-jaoasddet9ji3s8ki94qg1q3sbn5qpoc.apps.googleusercontent.com"
        
        # 已发现的API端点
        self.api_endpoints = {
            "status": "/api/status",
            "login": "/api/user/login",
            "logout": "/api/user/logout",
            "profile": "/api/user/profile",
            "user_info": "/api/user/info",
            "models": "/api/models",
            "github_callback": "/api/auth/github/callback"
        }
        
    def setup_driver(self, headless=True):
        """设置Chrome WebDriver"""
        chrome_options = Options()
        if headless:
            chrome_options.add_argument("--headless")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--window-size=1920,1080")
        chrome_options.add_argument(f"--user-agent={self.session.headers['User-Agent']}")
        
        # 创建临时用户数据目录
        temp_dir = tempfile.mkdtemp()
        chrome_options.add_argument(f"--user-data-dir={temp_dir}")
        
        try:
            driver = webdriver.Chrome(options=chrome_options)
            return driver, temp_dir
        except Exception as e:
            logging.error(f"Failed to setup Chrome driver: {e}")
            return None, None
    
    def github_oauth_flow(self, github_username: str, github_password: str) -> Optional[Dict]:
        """使用GitHub OAuth进行自动登录"""
        driver, temp_dir = self.setup_driver(headless=False)  # 显示浏览器以便调试
        
        if not driver:
            return None
            
        try:
            logging.info("🚀 Starting GitHub OAuth automation...")
            
            # 1. 访问OneRouter登录页面
            driver.get(f"{self.base_url}/login")
            time.sleep(2)
            
            # 2. 点击GitHub登录按钮
            github_btn = WebDriverWait(driver, 10).until(
                EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), 'Sign in with GitHub')]"))
            )
            github_btn.click()
            
            # 3. 等待跳转到GitHub
            WebDriverWait(driver, 10).until(
                lambda d: "github.com" in d.current_url
            )
            logging.info("✅ Redirected to GitHub")
            
            # 4. 填写GitHub登录信息
            username_field = WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.ID, "login_field"))
            )
            password_field = driver.find_element(By.ID, "password")
            
            username_field.send_keys(github_username)
            password_field.send_keys(github_password)
            
            # 5. 点击登录
            login_btn = driver.find_element(By.NAME, "commit")
            login_btn.click()
            
            # 6. 等待可能的2FA或授权页面
            time.sleep(3)
            
            # 检查是否需要授权应用
            if "authorize" in driver.current_url.lower():
                try:
                    authorize_btn = WebDriverWait(driver, 5).until(
                        EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), 'Authorize')]"))
                    )
                    authorize_btn.click()
                    logging.info("✅ Authorized OneRouter app")
                except:
                    logging.info("ℹ️ No authorization needed or already authorized")
            
            # 7. 等待回调到OneRouter
            WebDriverWait(driver, 15).until(
                lambda d: "onerouter.pro" in d.current_url and "login" not in d.current_url
            )
            
            logging.info("✅ Successfully logged in via GitHub OAuth")
            
            # 8. 获取cookies和session信息
            cookies = driver.get_cookies()
            current_url = driver.current_url
            
            # 9. 提取有用的信息
            session_cookies = {}
            for cookie in cookies:
                session_cookies[cookie['name']] = cookie['value']
            
            # 10. 尝试获取用户信息
            user_info = self.get_user_info_with_cookies(session_cookies)
            
            result = {
                "success": True,
                "cookies": session_cookies,
                "current_url": current_url,
                "user_info": user_info,
                "timestamp": time.time()
            }
            
            return result
            
        except Exception as e:
            logging.error(f"GitHub OAuth automation failed: {e}")
            return {"success": False, "error": str(e)}
            
        finally:
            driver.quit()
            # 清理临时目录
            try:
                import shutil
                shutil.rmtree(temp_dir)
            except:
                pass
    
    def get_user_info_with_cookies(self, cookies: Dict) -> Optional[Dict]:
        """使用cookies获取用户信息"""
        try:
            # 更新session cookies
            for name, value in cookies.items():
                self.session.cookies.set(name, value)
            
            # 尝试获取用户信息
            response = self.session.get(f"{self.base_url}{self.api_endpoints['user_info']}")
            
            if response.status_code == 200:
                return response.json()
            else:
                logging.warning(f"Failed to get user info: {response.status_code}")
                return None
                
        except Exception as e:
            logging.error(f"Error getting user info: {e}")
            return None
    
    def get_available_models(self, cookies: Dict) -> Optional[List]:
        """获取可用的AI模型列表"""
        try:
            # 更新session cookies
            for name, value in cookies.items():
                self.session.cookies.set(name, value)
            
            response = self.session.get(f"{self.base_url}{self.api_endpoints['models']}")
            
            if response.status_code == 200:
                return response.json()
            else:
                logging.warning(f"Failed to get models: {response.status_code}")
                return None
                
        except Exception as e:
            logging.error(f"Error getting models: {e}")
            return None
    
    def generate_api_key(self, cookies: Dict) -> Optional[str]:
        """尝试生成API密钥"""
        try:
            # 更新session cookies
            for name, value in cookies.items():
                self.session.cookies.set(name, value)
            
            # 尝试不同的可能端点
            potential_endpoints = [
                "/api/keys/generate",
                "/api/user/keys/generate", 
                "/api/auth/keys/generate",
                "/api/tokens/generate",
                "/api/user/tokens/generate"
            ]
            
            for endpoint in potential_endpoints:
                try:
                    response = self.session.post(f"{self.base_url}{endpoint}")
                    if response.status_code == 200:
                        data = response.json()
                        if 'key' in data or 'token' in data or 'api_key' in data:
                            logging.info(f"✅ API key generated via {endpoint}")
                            return data
                except:
                    continue
            
            logging.warning("❌ Could not find API key generation endpoint")
            return None
            
        except Exception as e:
            logging.error(f"Error generating API key: {e}")
            return None
    
    def explore_authenticated_endpoints(self, cookies: Dict) -> Dict:
        """探索需要认证的端点"""
        try:
            # 更新session cookies
            for name, value in cookies.items():
                self.session.cookies.set(name, value)
            
            # 更多可能的端点
            endpoints_to_test = [
                "/api/user/profile",
                "/api/user/info", 
                "/api/user/settings",
                "/api/user/keys",
                "/api/user/tokens",
                "/api/user/usage",
                "/api/user/billing",
                "/api/user/subscription",
                "/api/models",
                "/api/chat/completions",
                "/api/v1/chat/completions",
                "/api/openai/v1/chat/completions",
                "/api/dashboard",
                "/api/dashboard/stats",
                "/api/dashboard/usage",
                "/api/admin",
                "/api/admin/users",
                "/api/admin/stats"
            ]
            
            results = {}
            for endpoint in endpoints_to_test:
                try:
                    response = self.session.get(f"{self.base_url}{endpoint}")
                    results[endpoint] = {
                        "status_code": response.status_code,
                        "content_type": response.headers.get('content-type', ''),
                    }
                    
                    if response.status_code == 200:
                        try:
                            results[endpoint]["response"] = response.json()
                        except:
                            results[endpoint]["response"] = response.text[:200]
                            
                except Exception as e:
                    results[endpoint] = {"error": str(e)}
            
            return results
            
        except Exception as e:
            logging.error(f"Error exploring endpoints: {e}")
            return {}
    
    def save_session(self, session_data: Dict, filename: str = "onerouter_session.json"):
        """保存会话数据"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(session_data, f, indent=2, ensure_ascii=False)
            logging.info(f"✅ Session saved to {filename}")
        except Exception as e:
            logging.error(f"Failed to save session: {e}")
    
    def load_session(self, filename: str = "onerouter_session.json") -> Optional[Dict]:
        """加载会话数据"""
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            logging.error(f"Failed to load session: {e}")
            return None

def main():
    automation = OneRouterAutomation()
    
    print("🎯 OneRouter Automation Tool")
    print("=" * 50)
    
    # 注意：这里需要真实的GitHub账号信息
    github_username = input("Enter GitHub username: ").strip()
    github_password = input("Enter GitHub password: ").strip()
    
    if not github_username or not github_password:
        print("❌ GitHub credentials required")
        return
    
    print("\n🚀 Starting automation...")
    
    # 1. 执行GitHub OAuth流程
    result = automation.github_oauth_flow(github_username, github_password)
    
    if not result or not result.get('success'):
        print(f"❌ OAuth failed: {result.get('error', 'Unknown error')}")
        return
    
    print("✅ GitHub OAuth successful!")
    
    # 2. 保存会话
    automation.save_session(result)
    
    # 3. 探索认证后的端点
    print("\n🔍 Exploring authenticated endpoints...")
    endpoints = automation.explore_authenticated_endpoints(result['cookies'])
    
    # 4. 尝试获取模型列表
    print("\n📋 Getting available models...")
    models = automation.get_available_models(result['cookies'])
    
    # 5. 尝试生成API密钥
    print("\n🔑 Attempting to generate API key...")
    api_key = automation.generate_api_key(result['cookies'])
    
    # 6. 输出结果
    print("\n" + "=" * 50)
    print("📊 AUTOMATION RESULTS")
    print("=" * 50)
    
    print(f"✅ Login Status: Success")
    print(f"👤 User Info: {result.get('user_info', 'Not available')}")
    print(f"🔗 Current URL: {result.get('current_url')}")
    
    if models:
        print(f"📋 Models: {len(models)} available")
    else:
        print("📋 Models: Not accessible")
    
    if api_key:
        print(f"🔑 API Key: Generated successfully")
        print(f"    Key data: {api_key}")
    else:
        print("🔑 API Key: Generation failed")
    
    print(f"\n🔍 Accessible endpoints:")
    for endpoint, info in endpoints.items():
        if info.get('status_code') == 200:
            print(f"  ✅ {endpoint}: {info['status_code']}")
        elif info.get('status_code') in [401, 403]:
            print(f"  🔒 {endpoint}: {info['status_code']} (Auth required)")
    
    print(f"\n💾 Session data saved to: onerouter_session.json")

if __name__ == "__main__":
    main()