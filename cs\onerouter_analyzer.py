import requests
import json
import time
from typing import Dict, Optional
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class OneRouterAnalyzer:
    def __init__(self):
        self.base_url = "https://app.onerouter.pro"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Origin': 'https://app.onerouter.pro',
            'Referer': 'https://app.onerouter.pro/login',
        })
        
    def check_status(self) -> Dict:
        """检查API状态"""
        try:
            response = self.session.get(f"{self.base_url}/api/status")
            logging.info(f"Status API: {response.status_code}")
            if response.status_code == 200:
                return response.json()
            return {"error": f"Status code: {response.status_code}"}
        except Exception as e:
            logging.error(f"Status check failed: {e}")
            return {"error": str(e)}
    
    def attempt_login(self, email: str, password: str) -> Dict:
        """尝试登录"""
        login_data = {
            "email": email,
            "password": password
        }
        
        try:
            response = self.session.post(
                f"{self.base_url}/api/user/login",
                json=login_data,
                headers={'Content-Type': 'application/json'}
            )
            
            logging.info(f"Login attempt: {response.status_code}")
            result = {
                "status_code": response.status_code,
                "headers": dict(response.headers),
                "cookies": dict(response.cookies)
            }
            
            try:
                result["response"] = response.json()
            except:
                result["response"] = response.text
                
            return result
            
        except Exception as e:
            logging.error(f"Login failed: {e}")
            return {"error": str(e)}
    
    def explore_oauth_endpoints(self) -> Dict:
        """探索OAuth相关端点"""
        endpoints = {
            "github_oauth": f"{self.base_url}/api/auth/github",
            "google_oauth": f"{self.base_url}/api/auth/google",
            "oauth_callback": f"{self.base_url}/api/auth/callback"
        }
        
        results = {}
        for name, url in endpoints.items():
            try:
                response = self.session.get(url)
                results[name] = {
                    "status_code": response.status_code,
                    "headers": dict(response.headers)
                }
                if response.status_code == 200:
                    try:
                        results[name]["response"] = response.json()
                    except:
                        results[name]["response"] = response.text[:200]
            except Exception as e:
                results[name] = {"error": str(e)}
                
        return results
    
    def discover_api_endpoints(self) -> Dict:
        """发现可能的API端点"""
        potential_endpoints = [
            "/api/user/profile",
            "/api/user/info",
            "/api/user/register",
            "/api/user/logout",
            "/api/keys",
            "/api/keys/generate",
            "/api/models",
            "/api/usage",
            "/api/billing",
            "/api/subscription",
            "/api/chat/completions",
            "/api/v1/chat/completions",
            "/api/openai/chat/completions",
            "/api/proxy",
            "/api/config"
        ]
        
        results = {}
        for endpoint in potential_endpoints:
            try:
                response = self.session.get(f"{self.base_url}{endpoint}")
                results[endpoint] = {
                    "status_code": response.status_code,
                    "content_type": response.headers.get('content-type', ''),
                }
                
                # 只记录有意义的响应
                if response.status_code in [200, 401, 403]:
                    try:
                        results[endpoint]["response"] = response.json()
                    except:
                        text = response.text[:200]
                        if text.strip():
                            results[endpoint]["response"] = text
                            
            except Exception as e:
                results[endpoint] = {"error": str(e)}
                
        return results
    
    def analyze_github_oauth_flow(self) -> Dict:
        """分析GitHub OAuth流程"""
        # 从之前的分析中我们知道的信息
        oauth_info = {
            "client_id": "Ov23liOluRxWNZLIiX6k",
            "scope": "user:email",
            "authorize_url": "https://github.com/login/oauth/authorize",
            "redirect_uri": f"{self.base_url}/api/auth/github/callback"  # 推测的回调地址
        }
        
        # 尝试访问GitHub授权URL
        try:
            auth_url = f"{oauth_info['authorize_url']}?client_id={oauth_info['client_id']}&scope={oauth_info['scope']}&redirect_uri={oauth_info['redirect_uri']}"
            response = self.session.get(auth_url, allow_redirects=False)
            oauth_info["auth_url_status"] = response.status_code
            oauth_info["auth_url_headers"] = dict(response.headers)
        except Exception as e:
            oauth_info["auth_url_error"] = str(e)
            
        return oauth_info
    
    def full_analysis(self) -> Dict:
        """完整分析"""
        logging.info("🔍 Starting OneRouter API analysis...")
        
        analysis = {
            "timestamp": time.time(),
            "base_url": self.base_url,
            "status": self.check_status(),
            "oauth_github": self.analyze_github_oauth_flow(),
            "oauth_endpoints": self.explore_oauth_endpoints(),
            "api_endpoints": self.discover_api_endpoints(),
            "login_test": self.attempt_login("<EMAIL>", "testpassword123")
        }
        
        return analysis

def main():
    analyzer = OneRouterAnalyzer()
    results = analyzer.full_analysis()
    
    # 保存结果
    with open("onerouter_analysis.json", "w", encoding="utf-8") as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    # 打印关键发现
    print("\n" + "="*50)
    print("🎯 OneRouter API Analysis Results")
    print("="*50)
    
    print(f"\n📊 Status API: {results['status']}")
    
    print(f"\n🔐 GitHub OAuth Info:")
    oauth = results['oauth_github']
    print(f"  Client ID: {oauth.get('client_id')}")
    print(f"  Scope: {oauth.get('scope')}")
    
    print(f"\n🔍 Discovered API Endpoints:")
    for endpoint, info in results['api_endpoints'].items():
        if info.get('status_code') in [200, 401, 403]:
            print(f"  {endpoint}: {info['status_code']} - {info.get('content_type', '')}")
    
    print(f"\n🔑 Login Test Result: {results['login_test'].get('status_code')}")
    
    print(f"\n📄 Full analysis saved to: onerouter_analysis.json")

if __name__ == "__main__":
    main()