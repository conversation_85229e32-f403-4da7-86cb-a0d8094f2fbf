import requests

headers = {'Authorization': 'Bearer sk-freeplay-50ed7f046a384ab5b5c1ac84ce5b8b1f'}

try:
    response = requests.get('http://localhost:8000/admin/accounts/status', headers=headers)
    if response.status_code == 200:
        data = response.json()
        print(f'Total accounts: {data["total_accounts"]}')
        print(f'Healthy accounts: {data["healthy_accounts"]}')
        print(f'Total balance: {data["total_balance"]}')
        
        # 显示健康账户详情
        healthy_accounts = [acc for acc in data["accounts"] if float(acc["balance"].replace("$", "")) > 2.0]
        print(f'\nHealthy accounts (balance > $2.00):')
        for acc in healthy_accounts:
            print(f'  - {acc["email"]}: {acc["balance"]}')
    else:
        print(f'Error: {response.status_code} - {response.text}')
except Exception as e:
    print(f'Exception: {e}')