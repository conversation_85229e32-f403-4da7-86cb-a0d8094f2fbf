# 网易云音乐源 - 落雪音乐集成说明

## 🎯 概述

这是一个为落雪音乐(LX Music)开发的网易云音乐自定义源脚本，基于我们的网易云音乐API服务，提供高质量的音乐播放体验。

## ✨ 功能特性

- **高质量音源**：支持128k、320k、无损FLAC、Hi-Res音质
- **完整功能**：支持音乐播放、歌词显示、封面获取
- **稳定可靠**：基于官方API，具备完善的错误处理
- **实时更新**：支持最新的网易云音乐内容

## 📋 前置要求

### 1. 部署音乐API服务

首先需要将我们的网易云音乐API服务部署到服务器：

```bash
# 部署到Deno Deploy或其他云服务
# 确保服务可以通过HTTPS访问
```

### 2. 配置CORS

确保API服务支持跨域请求，在服务器响应中添加：

```javascript
headers: {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type'
}
```

## 🚀 安装步骤

### 步骤1：修改配置

1. 打开 `netease-music-lx-source.js` 文件
2. 修改第12行的 `BASE_URL` 为你的实际部署地址：

```javascript
const BASE_URL = 'https://your-domain.com' // 替换为你的域名
```

### 步骤2：导入到落雪音乐

1. 打开落雪音乐桌面版
2. 进入 **设置** → **自定义源**
3. 点击 **导入源** 按钮
4. 选择修改后的 `netease-music-lx-source.js` 文件
5. 等待导入完成

### 步骤3：启用音乐源

1. 在自定义源列表中找到 **网易云音乐源**
2. 点击启用开关
3. 设置为默认音源（可选）

## 🎵 使用方法

### 音质选择

在落雪音乐的设置中可以选择音质：

- **128k**：标准音质，适合网络较慢的环境
- **320k**：高音质，平衡音质和文件大小
- **FLAC**：无损音质，最佳音质体验
- **FLAC24bit**：Hi-Res音质，发烧友级别

### 功能支持

- ✅ **音乐播放**：支持所有音质的在线播放
- ✅ **歌词显示**：支持原文和翻译歌词
- ✅ **封面显示**：自动获取专辑封面
- ✅ **搜索功能**：通过落雪音乐的搜索功能使用
- ✅ **歌单导入**：支持网易云音乐歌单导入

## 🔧 故障排除

### 常见问题

1. **无法播放音乐**
   - 检查API服务是否正常运行
   - 确认BASE_URL配置正确
   - 检查网络连接

2. **音质选择无效**
   - 确认会员状态（高音质需要会员）
   - 检查API服务的Cookie配置

3. **歌词不显示**
   - 部分歌曲可能没有歌词
   - 检查API服务的歌词接口

### 调试方法

如需调试，可以修改脚本中的 `openDevTools` 为 `true`：

```javascript
send(EVENT_NAMES.inited, {
  openDevTools: true, // 开启开发者工具
  sources: {
    // ...
  }
})
```

## 📊 API接口说明

### 音乐播放接口
- **端点**：`/api/song`
- **方法**：POST
- **参数**：`{ids: string, level: string}`

### 歌词接口
- **端点**：`/api/lyrics`
- **方法**：POST
- **参数**：`{id: string}`

## ⚠️ 注意事项

1. **版权声明**：请遵守音乐版权法律法规，仅用于个人学习研究
2. **服务稳定性**：建议部署到稳定的云服务平台
3. **更新维护**：定期检查API服务状态和脚本更新

## 🔄 更新日志

### v1.0.0 (2025-06-20)
- 初始版本发布
- 支持基础音乐播放功能
- 支持歌词和封面获取
- 支持多种音质选择

## 📞 技术支持

如遇到问题，请检查：
1. API服务是否正常运行
2. 网络连接是否稳定
3. 落雪音乐版本是否支持自定义源

---

**享受高质量的音乐体验！** 🎵✨
