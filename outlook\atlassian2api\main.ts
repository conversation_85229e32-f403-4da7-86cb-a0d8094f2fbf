// main.ts - Deno Deploy 版本

/**
 * OpenAI 兼容的 API 服务器，部署在 Deno Deploy
 * 通过 Atlassian AI Gateway 代理请求到 Anthropic、Google 等模型。
 *
 * 特性：
 * • OpenAI 兼容 API
 * • 支持流式和非流式响应
 * • CORS 支持
 * • 自动重试和凭据轮换
 * • 环境变量配置
 *
 * 端点：
 *   GET  /v1/models
 *   POST /v1/chat/completions
 */

// ------------------------ Imports ------------------------
import { Application, Router, Status } from "https://deno.land/x/oak@v12.6.1/mod.ts";
import { TextLineStream } from "https://deno.land/std@0.224.0/streams/text_line_stream.ts";
import { oakCors } from "https://deno.land/x/cors@v1.2.2/mod.ts";

// ---------------------- Configuration --------------------
const DEBUG_MODE = Deno.env.get("DEBUG_MODE") === "true";

// 上游 Atlassian AI Gateway
const ROVO_DEV_PROXY_URL = "https://api.atlassian.com/rovodev/v2/proxy/ai";
const UNIFIED_CHAT_PATH = "/v2/beta/chat";
const ATLASSIAN_API_ENDPOINT = `${ROVO_DEV_PROXY_URL}${UNIFIED_CHAT_PATH}`;

// 支持的模型列表 (仅包含确认可用的模型)
const SUPPORTED_MODELS = [
  "anthropic:claude-3-5-sonnet-v2@20241022",
  "anthropic:claude-3-7-sonnet@20250219",
  "anthropic:claude-sonnet-4@20250514",
  "bedrock:anthropic.claude-3-5-sonnet-20241022-v2:0",
  "bedrock:anthropic.claude-3-7-sonnet-20250219-v1:0",
  "bedrock:anthropic.claude-sonnet-4-20250514-v1:0",
];

// 从环境变量获取凭据
function getCredentials(): Array<[string, string]> {
  const credentials: Array<[string, string]> = [];
  
  // 主凭据
  const email = Deno.env.get("ATLASSIAN_EMAIL");
  const token = Deno.env.get("ATLASSIAN_TOKEN");
  if (email && token) {
    credentials.push([email, token]);
  }
  
  // 备用凭据 (支持多个)
  for (let i = 1; i <= 5; i++) {
    const backupEmail = Deno.env.get(`ATLASSIAN_EMAIL_${i}`);
    const backupToken = Deno.env.get(`ATLASSIAN_TOKEN_${i}`);
    if (backupEmail && backupToken) {
      credentials.push([backupEmail, backupToken]);
    }
  }
  
  // 如果没有环境变量，使用默认凭据 (仅用于开发)
  if (credentials.length === 0) {
    console.warn("⚠️ 未找到环境变量凭据，使用默认凭据");
    credentials.push([
      "<EMAIL>", 
      "************************************************************************************************************************************************************************************************"
    ]);
  }
  
  return credentials;
}

const CREDENTIALS = getCredentials();

// --------------------- Helper Utilities ------------------

/** 构造 Atlassian 基础认证头 */
function buildAuthHeaders(email: string, apiToken: string): Headers {
  const encoded = btoa(`${email}:${apiToken}`);
  return new Headers({
    "Content-Type": "application/json",
    "Accept": "application/json",
    "Authorization": `Basic ${encoded}`,
    "X-Atlassian-EncodedToken": encoded,
  });
}

/**
 * 从模型 ID 中移除前缀 (`anthropic:` / `google:` 等)。
 */
function transformModelId(modelId: string): string {
  return modelId.split(":").pop() ?? modelId;
}

/**
 * 将 Atlassian 非流式响应转换为 OpenAI 兼容格式。
 */
function toOpenAI(atlas: any, modelId: string) {
  const usage = atlas.platform_attributes?.metrics?.usage || {};
  return {
    id: atlas.response_payload.id,
    object: "chat.completion",
    created: atlas.response_payload.created,
    model: modelId,
    choices: atlas.response_payload.choices.map((c: any) => ({
      index: c.index,
      message: c.message,
      finish_reason: c.finish_reason,
    })),
    usage: {
      prompt_tokens: usage.prompt_tokens,
      completion_tokens: usage.completion_tokens,
      total_tokens: usage.total_tokens,
    },
  };
}

// ----------------------- Streaming -----------------------
class StreamProcessor extends TransformStream<string, Uint8Array> {
  private isFirst = true;
  constructor(private readonly requestedModelId: string) {
    super({
      transform: (chunk, ctl) => this.handle(chunk, ctl),
      flush: (ctl) => this.done(ctl),
    });
  }

  handle(chunk: string, ctl: TransformStreamDefaultController<Uint8Array>) {
    if (!chunk || !chunk.trim().startsWith("data:")) return;
    const payload = chunk.trim().slice(5).trim();

    if (payload === "[DONE]") return; // 上游 DONE 信号忽略

    let parsed;
    try {
      parsed = JSON.parse(payload);
    } catch {
      DEBUG_MODE && console.error("无法解析 JSON:", payload);
      return;
    }

    const choice = parsed.response_payload?.choices?.[0];
    if (!choice) return;

    const delta: Record<string, unknown> = {};
    if (this.isFirst && choice.message?.role) {
      delta.role = choice.message.role;
      this.isFirst = false;
    }

    const content = choice.message?.content?.[0]?.text;
    if (content) delta.content = content;

    if (Object.keys(delta).length === 0 && !choice.finish_reason) return;

    const openChunk = {
      id: parsed.response_payload.id || `chatcmpl-${Date.now()}`,
      object: "chat.completion.chunk",
      created: parsed.response_payload.created || Math.floor(Date.now() / 1000),
      model: this.requestedModelId,
      choices: [{
        index: choice.index,
        delta,
        finish_reason: choice.finish_reason ?? null,
      }],
    };

    ctl.enqueue(new TextEncoder().encode(`data: ${JSON.stringify(openChunk)}\n\n`));
  }

  done(ctl: TransformStreamDefaultController<Uint8Array>) {
    ctl.enqueue(new TextEncoder().encode("data: [DONE]\n\n"));
  }
}

// ------------- Upstream Request w/ Retry & Backoff -------------
async function fetchWithRetry(body: unknown, credentialStart = 0): Promise<Response> {
  let attempt = 0;
  let delay = 500; // 0.5s
  let credIdx = credentialStart % CREDENTIALS.length;

  while (attempt < CREDENTIALS.length) {
    const [email, token] = CREDENTIALS[credIdx];
    const headers = buildAuthHeaders(email, token);
    try {
      const resp = await fetch(ATLASSIAN_API_ENDPOINT, {
        method: "POST",
        headers,
        body: JSON.stringify(body),
      });

      if (resp.ok) return resp; // ✅ 成功

      if ([401, 403].includes(resp.status) || resp.status >= 500) {
        DEBUG_MODE && console.warn(`凭据 #${credIdx} 失败 (${resp.status}), 尝试下一个...`);
        // 失败则等待退避后尝试下一个凭据
        await new Promise((r) => setTimeout(r, delay));
        delay = Math.min(delay * 2, 16_000);
        credIdx = (credIdx + 1) % CREDENTIALS.length;
        attempt++;
        continue;
      }
      // 其他错误直接抛出
      throw new Error(`Upstream error ${resp.status}`);
    } catch (err) {
      // 网络层错误 -> 退避 & 切换凭据
      DEBUG_MODE && console.error("fetch error", err);
      await new Promise((r) => setTimeout(r, delay));
      delay = Math.min(delay * 2, 16_000);
      credIdx = (credIdx + 1) % CREDENTIALS.length;
      attempt++;
    }
  }
  throw new Error("All credentials exhausted.");
}

// ----------------------- Route Handlers ------------------
function modelsHandler() {
  const now = Math.floor(Date.now() / 1000);
  return {
    object: "list",
    data: SUPPORTED_MODELS.map((id) => ({ id, object: "model", created: now, owned_by: "system" })),
  };
}

async function chatHandler(ctx: any) {
  if (ctx.request.method !== "POST") {
    ctx.response.status = Status.MethodNotAllowed;
    ctx.response.body = { error: "Method Not Allowed" };
    return;
  }

  const bodyJson = await ctx.request.body({ type: "json" }).value;
  const requestedModel = bodyJson.model;

  // 验证模型是否支持
  if (!SUPPORTED_MODELS.includes(requestedModel)) {
    ctx.response.status = Status.BadRequest;
    ctx.response.body = { 
      error: `Model ${requestedModel} not supported`,
      supported_models: SUPPORTED_MODELS
    };
    return;
  }

  const upstreamBody = {
    request_payload: {
      messages: bodyJson.messages,
      temperature: bodyJson.temperature,
      stream: bodyJson.stream || false,
    },
    platform_attributes: {
      model: transformModelId(requestedModel),
    },
  };

  try {
    const upstream = await fetchWithRetry(upstreamBody, 0);

    if (bodyJson.stream) {
      if (!upstream.body) {
        ctx.throw(Status.InternalServerError, "Upstream has no body");
      }
      const processed = upstream.body
        .pipeThrough(new TextDecoderStream())
        .pipeThrough(new TextLineStream())
        .pipeThrough(new StreamProcessor(requestedModel));

      ctx.response.type = "text/event-stream";
      ctx.response.headers.set("Cache-Control", "no-cache");
      ctx.response.headers.set("Connection", "keep-alive");
      ctx.response.body = processed;
    } else {
      const json = await upstream.json();
      ctx.response.body = toOpenAI(json, requestedModel);
    }
  } catch (err) {
    DEBUG_MODE && console.error(err);
    ctx.response.status = Status.InternalServerError;
    ctx.response.body = { 
      error: "Upstream request failed",
      message: err.message
    };
  }
}

// 健康检查端点
function healthHandler(ctx: any) {
  ctx.response.body = {
    status: "ok",
    timestamp: new Date().toISOString(),
    models: SUPPORTED_MODELS.length,
    credentials: CREDENTIALS.length
  };
}

// ------------------------ Server -------------------------
const router = new Router();
router
  .get("/", healthHandler)
  .get("/health", healthHandler)
  .get("/v1/models", (ctx) => (ctx.response.body = modelsHandler()))
  .post("/v1/chat/completions", chatHandler);

const app = new Application();

// CORS 中间件
app.use(oakCors({ 
  origin: "*",
  methods: ["GET", "POST", "OPTIONS"],
  allowedHeaders: ["Content-Type", "Authorization"]
}));

// 错误处理中间件
app.use(async (ctx, next) => {
  try {
    await next();
  } catch (err) {
    console.error("Server error:", err);
    ctx.response.status = Status.InternalServerError;
    ctx.response.body = { error: "Internal server error" };
  }
});

app.use(router.routes());
app.use(router.allowedMethods());

// Deno Deploy 使用动态端口
const PORT = parseInt(Deno.env.get("PORT") || "8000");
console.log(`🚀 OpenAI Compatible API Server`);
console.log(`📡 Listening on port ${PORT}`);
console.log(`🔑 Loaded ${CREDENTIALS.length} credential(s)`);
console.log(`🤖 Supporting ${SUPPORTED_MODELS.length} model(s)`);

await app.listen({ port: PORT });