# OpenAI Compatible API Server

🚀 **部署在 Deno Deploy 的 OpenAI 兼容 API 服务器**

通过 Atlassian AI Gateway 代理访问 Claude 等 AI 模型，完全兼容 OpenAI API 格式。

## ⚡ 快速部署

### 1. 部署到 Deno Deploy
1. 访问 [Deno Deploy](https://dash.deno.com/)
2. 创建新项目
   - **项目名称**：使用 `openai-proxy`, `claude-api`, `ai-gateway` 等符合规范的名称
   - 选择 `main.ts` 作为入口文件
3. 配置环境变量：
   ```
   ATLASSIAN_EMAIL=<EMAIL>
   ATLASSIAN_TOKEN=your-atlassian-api-token
   ```

### 2. 使用 API
```bash
# 获取模型列表
curl https://your-project.deno.dev/v1/models

# 聊天完成
curl -X POST https://your-project.deno.dev/v1/chat/completions \
  -H "Content-Type: application/json" \
  -d '{
    "model": "anthropic:claude-3-5-sonnet-v2@20241022",
    "messages": [{"role": "user", "content": "Hello!"}]
  }'
```

## 🤖 支持的模型

- `anthropic:claude-3-5-sonnet-v2@20241022` ⭐ (推荐)
- `anthropic:claude-3-7-sonnet@20250219`
- `anthropic:claude-sonnet-4@20250514`
- `bedrock:anthropic.claude-3-5-sonnet-20241022-v2:0`
- `bedrock:anthropic.claude-3-7-sonnet-20250219-v1:0`
- `bedrock:anthropic.claude-sonnet-4-20250514-v1:0`

## 🔧 在 Cherry Studio 中使用

1. 设置 Base URL: `https://your-project.deno.dev`
2. API Key: 任意值（如 `sk-dummy`）
3. 选择模型开始对话

## 📁 文件说明

- `main.ts` - 主服务器文件
- `deno.json` - Deno 配置文件
- `README-Deploy.md` - 详细部署指南

## 🚀 本地开发

```bash
# 设置环境变量
export ATLASSIAN_EMAIL="<EMAIL>"
export ATLASSIAN_TOKEN="your-atlassian-api-token"

# 运行服务器
deno task start
```

## 📞 健康检查

```bash
curl https://your-project.deno.dev/health
```

---

🎉 **现在你有了一个部署在云端的 OpenAI 兼容 API 服务器！**

详细部署指南请查看 [`README-Deploy.md`](README-Deploy.md)