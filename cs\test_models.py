import requests
import json

headers = {
    'Authorization': 'Bearer sk-freeplay-50ed7f046a384ab5b5c1ac84ce5b8b1f',
    'Content-Type': 'application/json'
}

# 测试所有模型
models = [
    "claude-3-7-sonnet-20250219",
    "claude-4-opus-20250514", 
    "claude-4-sonnet",
    "o3-2025-04-16",
    "gemini-2.5-pro-preview-06-05",
    "gemini-2.5-pro-preview-05-06",
    "gemini-2.5-pro-preview-03-25"
]

# 首先获取可用模型列表
print("Available models:")
response = requests.get('http://localhost:8000/v1/models', headers=headers)
if response.status_code == 200:
    model_data = response.json()
    for model in model_data['data']:
        print(f"- {model['id']} (by {model['owned_by']})")
else:
    print(f"Failed to get models: {response.status_code}")

print("\nTesting each model with a simple request...")

for model in models:
    print(f"\nTesting {model}...")
    
    payload = {
        "model": model,
        "messages": [
            {"role": "user", "content": "Hello! Please respond with just 'Hi' to test the connection."}
        ],
        "max_tokens": 10,
        "temperature": 0.1
    }
    
    try:
        response = requests.post('http://localhost:8000/v1/chat/completions', 
                               headers=headers, 
                               json=payload,
                               timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            content = data['choices'][0]['message']['content']
            print(f"✅ {model}: {content.strip()}")
        else:
            print(f"❌ {model}: HTTP {response.status_code} - {response.text}")
            
    except Exception as e:
        print(f"❌ {model}: Error - {str(e)}")