import requests
import json

headers = {'Authorization': 'Bearer sk-freeplay-50ed7f046a384ab5b5c1ac84ce5b8b1f'}

# 测试手动注册
print("Testing manual registration...")
response = requests.post('http://localhost:8000/admin/register/manual', headers=headers)
print('Registration Response:', response.json())

# 检查账户状态
print("\nChecking account status...")
response = requests.get('http://localhost:8000/admin/accounts/status', headers=headers)
data = response.json()
print(f'Total accounts: {data["total_accounts"]}')
print(f'Healthy accounts: {data["healthy_accounts"]}')
print(f'Total balance: {data["total_balance"]}')