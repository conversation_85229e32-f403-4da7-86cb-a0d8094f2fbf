import requests
import json
import time
import random
import string
from typing import Dict, List, Optional
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
import tempfile
import threading
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class TempEmailService:
    """临时邮箱服务管理器"""
    
    def __init__(self):
        self.services = {
            "10minutemail": {
                "base_url": "https://10minutemail.com",
                "api_url": "https://10minutemail.com/10MinuteMail/index.html"
            },
            "tempmail": {
                "base_url": "https://temp-mail.org",
                "api_url": "https://api.temp-mail.org/request"
            },
            "guerrillamail": {
                "base_url": "https://guerrillamail.com",
                "api_url": "https://api.guerrillamail.com/ajax.php"
            }
        }
        
    def get_temp_email_10min(self) -> Optional[Dict]:
        """获取10分钟邮箱"""
        try:
            session = requests.Session()
            session.headers.update({
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            })
            
            # 访问主页获取邮箱
            response = session.get("https://10minutemail.com/10MinuteMail/index.html")
            if response.status_code == 200:
                # 这里需要解析HTML来获取邮箱地址
                # 简化版本：生成随机邮箱格式
                random_id = ''.join(random.choices(string.ascii_lowercase + string.digits, k=10))
                email = f"{random_id}@10minutemail.com"
                
                return {
                    "email": email,
                    "service": "10minutemail",
                    "session": session,
                    "expires_at": time.time() + 600  # 10分钟
                }
        except Exception as e:
            logging.error(f"Failed to get 10minutemail: {e}")
            return None
    
    def get_temp_email_guerrilla(self) -> Optional[Dict]:
        """获取Guerrilla邮箱"""
        try:
            session = requests.Session()
            
            # 获取新的邮箱地址
            response = session.get("https://api.guerrillamail.com/ajax.php", params={
                "f": "get_email_address",
                "lang": "en"
            })
            
            if response.status_code == 200:
                data = response.json()
                return {
                    "email": data.get("email_addr"),
                    "service": "guerrillamail", 
                    "session": session,
                    "sid_token": data.get("sid_token"),
                    "expires_at": time.time() + 3600  # 1小时
                }
        except Exception as e:
            logging.error(f"Failed to get guerrillamail: {e}")
            return None
    
    def generate_fake_email(self) -> Dict:
        """生成虚假邮箱（用于测试）"""
        domains = ["gmail.com", "yahoo.com", "hotmail.com", "outlook.com", "protonmail.com"]
        username = ''.join(random.choices(string.ascii_lowercase + string.digits, k=12))
        domain = random.choice(domains)
        
        return {
            "email": f"{username}@{domain}",
            "service": "fake",
            "session": None,
            "expires_at": time.time() + 86400  # 24小时
        }

class OneRouterBatchRegister:
    """OneRouter批量注册器"""
    
    def __init__(self):
        self.base_url = "https://app.onerouter.pro"
        self.temp_email = TempEmailService()
        self.registered_accounts = []
        
        # GitHub账号池（需要真实账号）
        self.github_accounts = [
            # {"username": "user1", "password": "pass1"},
            # {"username": "user2", "password": "pass2"},
            # 添加更多GitHub账号
        ]
        
    def setup_driver(self, headless=True, proxy=None):
        """设置Chrome WebDriver"""
        chrome_options = Options()
        if headless:
            chrome_options.add_argument("--headless")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--window-size=1920,1080")
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        if proxy:
            chrome_options.add_argument(f"--proxy-server={proxy}")
        
        # 随机User-Agent
        user_agents = [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
        ]
        chrome_options.add_argument(f"--user-agent={random.choice(user_agents)}")
        
        temp_dir = tempfile.mkdtemp()
        chrome_options.add_argument(f"--user-data-dir={temp_dir}")
        
        try:
            driver = webdriver.Chrome(options=chrome_options)
            driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            return driver, temp_dir
        except Exception as e:
            logging.error(f"Failed to setup Chrome driver: {e}")
            return None, None
    
    def register_via_github_oauth(self, github_account: Dict, proxy=None) -> Optional[Dict]:
        """通过GitHub OAuth注册"""
        driver, temp_dir = self.setup_driver(headless=True, proxy=proxy)
        
        if not driver:
            return None
            
        try:
            logging.info(f"🚀 Starting GitHub OAuth for {github_account['username']}")
            
            # 1. 访问OneRouter登录页面
            driver.get(f"{self.base_url}/login")
            time.sleep(random.uniform(2, 4))
            
            # 2. 点击GitHub登录按钮
            github_btn = WebDriverWait(driver, 10).until(
                EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), 'Sign in with GitHub')]"))
            )
            github_btn.click()
            
            # 3. 等待跳转到GitHub
            WebDriverWait(driver, 10).until(
                lambda d: "github.com" in d.current_url
            )
            
            # 4. 检查是否已经登录GitHub
            if "login" in driver.current_url:
                # 需要登录GitHub
                username_field = WebDriverWait(driver, 10).until(
                    EC.presence_of_element_located((By.ID, "login_field"))
                )
                password_field = driver.find_element(By.ID, "password")
                
                username_field.send_keys(github_account['username'])
                time.sleep(random.uniform(1, 2))
                password_field.send_keys(github_account['password'])
                time.sleep(random.uniform(1, 2))
                
                login_btn = driver.find_element(By.NAME, "commit")
                login_btn.click()
                
                # 等待可能的2FA
                time.sleep(5)
                
                # 检查是否需要2FA
                if "two-factor" in driver.current_url or "2fa" in driver.current_url:
                    logging.warning("⚠️ 2FA required, skipping this account")
                    return None
            
            # 5. 处理授权页面
            if "authorize" in driver.current_url.lower():
                try:
                    authorize_btn = WebDriverWait(driver, 5).until(
                        EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), 'Authorize')]"))
                    )
                    authorize_btn.click()
                except:
                    pass
            
            # 6. 等待回调到OneRouter
            WebDriverWait(driver, 15).until(
                lambda d: "onerouter.pro" in d.current_url and "login" not in d.current_url
            )
            
            # 7. 获取cookies
            cookies = {}
            for cookie in driver.get_cookies():
                cookies[cookie['name']] = cookie['value']
            
            # 8. 获取用户信息
            user_info = self.get_user_info_with_cookies(cookies)
            
            account_data = {
                "github_username": github_account['username'],
                "cookies": cookies,
                "user_info": user_info,
                "registered_at": time.time(),
                "proxy": proxy
            }
            
            logging.info(f"✅ Successfully registered via GitHub: {github_account['username']}")
            return account_data
            
        except Exception as e:
            logging.error(f"❌ GitHub OAuth failed for {github_account['username']}: {e}")
            return None
            
        finally:
            driver.quit()
            try:
                import shutil
                shutil.rmtree(temp_dir)
            except:
                pass
    
    def register_via_email(self, email_data: Dict, proxy=None) -> Optional[Dict]:
        """通过邮箱注册（如果支持）"""
        # OneRouter可能不支持直接邮箱注册，但可以尝试
        session = requests.Session()
        if proxy:
            session.proxies = {"http": proxy, "https": proxy}
        
        session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'application/json',
            'Content-Type': 'application/json',
            'Origin': self.base_url,
            'Referer': f'{self.base_url}/register'
        })
        
        # 生成随机密码
        password = ''.join(random.choices(string.ascii_letters + string.digits + "!@#$%", k=12))
        
        register_data = {
            "email": email_data["email"],
            "password": password,
            "confirm_password": password
        }
        
        try:
            # 尝试注册端点
            response = session.post(f"{self.base_url}/api/user/register", json=register_data)
            
            if response.status_code == 200:
                result = response.json()
                if result.get("success"):
                    return {
                        "email": email_data["email"],
                        "password": password,
                        "response": result,
                        "registered_at": time.time(),
                        "proxy": proxy
                    }
            
            logging.warning(f"Email registration failed: {response.status_code}")
            return None
            
        except Exception as e:
            logging.error(f"Email registration error: {e}")
            return None
    
    def get_user_info_with_cookies(self, cookies: Dict) -> Optional[Dict]:
        """使用cookies获取用户信息"""
        try:
            session = requests.Session()
            for name, value in cookies.items():
                session.cookies.set(name, value)
            
            session.headers.update({
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'Accept': 'application/json',
                'Referer': f'{self.base_url}/dashboard'
            })
            
            response = session.get(f"{self.base_url}/api/user/info")
            if response.status_code == 200:
                return response.json()
            return None
            
        except Exception as e:
            logging.error(f"Error getting user info: {e}")
            return None
    
    def batch_register_github(self, count: int = 5, use_proxy=False) -> List[Dict]:
        """批量GitHub注册"""
        if not self.github_accounts:
            logging.error("❌ No GitHub accounts provided")
            return []
        
        results = []
        available_accounts = self.github_accounts[:count]
        
        for i, github_account in enumerate(available_accounts):
            logging.info(f"📝 Registering account {i+1}/{len(available_accounts)}")
            
            proxy = None
            if use_proxy:
                # 这里可以集成代理池
                proxy = self.get_random_proxy()
            
            result = self.register_via_github_oauth(github_account, proxy)
            if result:
                results.append(result)
                self.registered_accounts.append(result)
            
            # 随机延迟避免被检测
            time.sleep(random.uniform(10, 30))
        
        return results
    
    def batch_register_temp_email(self, count: int = 5) -> List[Dict]:
        """批量临时邮箱注册"""
        results = []
        
        for i in range(count):
            logging.info(f"📧 Creating temp email account {i+1}/{count}")
            
            # 尝试不同的临时邮箱服务
            email_data = None
            for service_method in [
                self.temp_email.get_temp_email_guerrilla,
                self.temp_email.get_temp_email_10min,
                self.temp_email.generate_fake_email
            ]:
                email_data = service_method()
                if email_data:
                    break
            
            if not email_data:
                logging.error("❌ Failed to get temp email")
                continue
            
            result = self.register_via_email(email_data)
            if result:
                results.append(result)
                self.registered_accounts.append(result)
            
            time.sleep(random.uniform(5, 15))
        
        return results
    
    def get_random_proxy(self) -> Optional[str]:
        """获取随机代理（需要实现代理池）"""
        # 这里可以集成免费代理池或付费代理服务
        proxies = [
            # "http://proxy1:port",
            # "http://proxy2:port",
        ]
        return random.choice(proxies) if proxies else None
    
    def save_accounts(self, filename: str = "onerouter_accounts.json"):
        """保存注册的账号"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(self.registered_accounts, f, indent=2, ensure_ascii=False)
            logging.info(f"✅ Accounts saved to {filename}")
        except Exception as e:
            logging.error(f"Failed to save accounts: {e}")
    
    def load_accounts(self, filename: str = "onerouter_accounts.json") -> List[Dict]:
        """加载已注册的账号"""
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                self.registered_accounts = json.load(f)
            logging.info(f"✅ Loaded {len(self.registered_accounts)} accounts")
            return self.registered_accounts
        except Exception as e:
            logging.error(f"Failed to load accounts: {e}")
            return []

def main():
    register = OneRouterBatchRegister()
    
    print("🎯 OneRouter 批量注册工具")
    print("=" * 50)
    
    while True:
        print("\n选择注册方式:")
        print("1. GitHub OAuth 批量注册")
        print("2. 临时邮箱批量注册")
        print("3. 查看已注册账号")
        print("4. 保存账号数据")
        print("5. 加载账号数据")
        print("6. 退出")
        
        choice = input("\n请选择 (1-6): ").strip()
        
        if choice == "1":
            if not register.github_accounts:
                print("❌ 请先在代码中添加GitHub账号信息")
                continue
                
            count = int(input("注册数量: ") or "3")
            use_proxy = input("使用代理? (y/n): ").lower() == 'y'
            
            print(f"\n🚀 开始批量注册 {count} 个账号...")
            results = register.batch_register_github(count, use_proxy)
            print(f"✅ 成功注册 {len(results)} 个账号")
            
        elif choice == "2":
            count = int(input("注册数量: ") or "3")
            
            print(f"\n📧 开始临时邮箱注册 {count} 个账号...")
            results = register.batch_register_temp_email(count)
            print(f"✅ 成功注册 {len(results)} 个账号")
            
        elif choice == "3":
            accounts = register.registered_accounts
            print(f"\n📋 已注册账号数量: {len(accounts)}")
            for i, acc in enumerate(accounts[:5]):  # 显示前5个
                print(f"  {i+1}. {acc.get('github_username', acc.get('email', 'Unknown'))}")
            if len(accounts) > 5:
                print(f"  ... 还有 {len(accounts)-5} 个账号")
                
        elif choice == "4":
            filename = input("保存文件名 (默认: onerouter_accounts.json): ").strip()
            register.save_accounts(filename or "onerouter_accounts.json")
            
        elif choice == "5":
            filename = input("加载文件名 (默认: onerouter_accounts.json): ").strip()
            register.load_accounts(filename or "onerouter_accounts.json")
            
        elif choice == "6":
            print("👋 再见!")
            break
            
        else:
            print("❌ 无效选择")

if __name__ == "__main__":
    main()