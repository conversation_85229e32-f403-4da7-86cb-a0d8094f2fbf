#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
临时邮箱批量注册OneRouter账号
并将成功的账号信息保存到cc.txt
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from onerouter_batch_register import OneRouterBatchRegister, TempEmailService
import json
import time
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def save_successful_accounts(accounts, filename="cc.txt"):
    """保存成功的账号到cc.txt文件"""
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            f.write("=" * 60 + "\n")
            f.write("OneRouter 临时邮箱注册成功账号列表\n")
            f.write(f"生成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write("=" * 60 + "\n\n")
            
            for i, account in enumerate(accounts, 1):
                f.write(f"账号 #{i}\n")
                f.write("-" * 30 + "\n")
                f.write(f"邮箱: {account.get('email', 'N/A')}\n")
                f.write(f"密码: {account.get('password', 'N/A')}\n")
                f.write(f"注册时间: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(account.get('registered_at', time.time())))}\n")
                
                # 如果有API相关信息
                if 'api_key' in account:
                    f.write(f"API密钥: {account['api_key']}\n")
                
                if 'cookies' in account:
                    f.write("Cookies:\n")
                    for name, value in account['cookies'].items():
                        f.write(f"  {name}: {value}\n")
                
                if 'user_info' in account and account['user_info']:
                    f.write(f"用户信息: {json.dumps(account['user_info'], ensure_ascii=False, indent=2)}\n")
                
                f.write("\n")
            
            f.write("=" * 60 + "\n")
            f.write(f"总计成功注册: {len(accounts)} 个账号\n")
            f.write("=" * 60 + "\n")
        
        print(f"✅ 成功账号信息已保存到: {filename}")
        
    except Exception as e:
        print(f"❌ 保存账号信息失败: {e}")

def enhanced_temp_email_register(count=10):
    """增强版临时邮箱注册"""
    print(f"🚀 开始临时邮箱批量注册 {count} 个OneRouter账号...")
    
    register = OneRouterBatchRegister()
    temp_email_service = TempEmailService()
    successful_accounts = []
    
    for i in range(count):
        print(f"\n📧 正在注册第 {i+1}/{count} 个账号...")
        
        # 尝试不同的临时邮箱服务
        email_data = None
        
        # 1. 尝试Guerrilla邮箱
        try:
            email_data = temp_email_service.get_temp_email_guerrilla()
            if email_data:
                print(f"✅ 获取Guerrilla邮箱: {email_data['email']}")
        except Exception as e:
            print(f"⚠️ Guerrilla邮箱获取失败: {e}")
        
        # 2. 如果失败，尝试10分钟邮箱
        if not email_data:
            try:
                email_data = temp_email_service.get_temp_email_10min()
                if email_data:
                    print(f"✅ 获取10分钟邮箱: {email_data['email']}")
            except Exception as e:
                print(f"⚠️ 10分钟邮箱获取失败: {e}")
        
        # 3. 最后使用虚假邮箱
        if not email_data:
            email_data = temp_email_service.generate_fake_email()
            print(f"✅ 生成虚假邮箱: {email_data['email']}")
        
        # 尝试注册
        if email_data:
            result = register.register_via_email(email_data)
            
            if result:
                print(f"✅ 注册成功: {result['email']}")
                successful_accounts.append(result)
                
                # 尝试获取更多信息
                try:
                    # 如果注册成功，尝试登录获取cookies
                    login_result = register.attempt_login(result['email'], result['password'])
                    if login_result and login_result.get('cookies'):
                        result['cookies'] = login_result['cookies']
                        
                        # 尝试获取用户信息
                        user_info = register.get_user_info_with_cookies(login_result['cookies'])
                        if user_info:
                            result['user_info'] = user_info
                            print(f"✅ 获取用户信息成功")
                        
                        # 尝试获取API密钥
                        api_key = register.generate_api_key(login_result['cookies'])
                        if api_key:
                            result['api_key'] = api_key
                            print(f"✅ 获取API密钥成功")
                            
                except Exception as e:
                    print(f"⚠️ 获取额外信息失败: {e}")
                    
            else:
                print(f"❌ 注册失败: {email_data['email']}")
        
        # 随机延迟避免被检测
        import random
        delay = random.uniform(5, 15)
        print(f"⏳ 等待 {delay:.1f} 秒...")
        time.sleep(delay)
    
    print(f"\n🎉 批量注册完成！")
    print(f"✅ 成功注册: {len(successful_accounts)} 个账号")
    print(f"❌ 失败: {count - len(successful_accounts)} 个账号")
    
    # 保存成功的账号
    if successful_accounts:
        save_successful_accounts(successful_accounts, "cc.txt")
        
        # 也保存JSON格式备份
        try:
            with open("successful_accounts.json", 'w', encoding='utf-8') as f:
                json.dump(successful_accounts, f, indent=2, ensure_ascii=False)
            print(f"✅ JSON备份已保存到: successful_accounts.json")
        except Exception as e:
            print(f"⚠️ JSON备份保存失败: {e}")
    
    return successful_accounts

def attempt_login(register, email, password):
    """尝试登录获取cookies"""
    try:
        import requests
        
        session = requests.Session()
        session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'application/json',
            'Content-Type': 'application/json',
            'Origin': register.base_url,
            'Referer': f'{register.base_url}/login'
        })
        
        login_data = {
            "email": email,
            "password": password
        }
        
        response = session.post(f"{register.base_url}/api/user/login", json=login_data)
        
        if response.status_code == 200:
            result = response.json()
            if result.get("success"):
                # 获取cookies
                cookies = {}
                for cookie in session.cookies:
                    cookies[cookie.name] = cookie.value
                
                return {
                    "success": True,
                    "cookies": cookies,
                    "response": result
                }
        
        return None
        
    except Exception as e:
        print(f"登录尝试失败: {e}")
        return None

def main():
    """主函数"""
    print("🎯 OneRouter 临时邮箱批量注册工具")
    print("=" * 50)
    
    # 获取注册数量
    try:
        count = int(input("请输入要注册的账号数量 (默认10): ") or "10")
    except ValueError:
        count = 10
    
    if count <= 0 or count > 50:
        print("❌ 注册数量应在1-50之间")
        return
    
    print(f"📝 将注册 {count} 个临时邮箱账号...")
    
    # 确认开始
    confirm = input("确认开始注册? (y/n): ").lower()
    if confirm != 'y':
        print("❌ 取消注册")
        return
    
    # 开始注册
    successful_accounts = enhanced_temp_email_register(count)
    
    # 显示结果
    print(f"\n📊 最终结果:")
    print(f"✅ 成功注册: {len(successful_accounts)} 个账号")
    
    if successful_accounts:
        print(f"📄 账号信息已保存到: cc.txt")
        print(f"📄 JSON备份已保存到: successful_accounts.json")
        
        print(f"\n📋 成功账号列表:")
        for i, acc in enumerate(successful_accounts[:5], 1):  # 显示前5个
            print(f"  {i}. {acc['email']}")
        
        if len(successful_accounts) > 5:
            print(f"  ... 还有 {len(successful_accounts) - 5} 个账号")
    
    print(f"\n🎉 注册完成！")

if __name__ == "__main__":
    main()