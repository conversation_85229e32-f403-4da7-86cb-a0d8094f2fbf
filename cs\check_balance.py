import requests
import json

headers = {'Authorization': 'Bearer sk-freeplay-50ed7f046a384ab5b5c1ac84ce5b8b1f'}

try:
    response = requests.get('http://localhost:8000/admin/accounts/status', headers=headers)
    if response.status_code == 200:
        data = response.json()
        print("=== 账号池状态 ===")
        print(f"总账户数: {data['total_accounts']}")
        print(f"健康账户数: {data['healthy_accounts']}")
        print(f"总余额: {data['total_balance']}")
        
        print("\n=== 账户详情 ===")
        accounts = data['accounts']
        
        # 按余额排序
        accounts_sorted = sorted(accounts, key=lambda x: float(x['balance'].replace('$', '')), reverse=True)
        
        positive_balance = 0
        negative_balance = 0
        zero_balance = 0
        
        for acc in accounts_sorted:
            balance_value = float(acc['balance'].replace('$', ''))
            status = ""
            if balance_value > 0:
                status = "✅ 正余额"
                positive_balance += 1
            elif balance_value == 0:
                status = "⚪ 零余额"
                zero_balance += 1
            else:
                status = "❌ 负余额"
                negative_balance += 1
                
            print(f"{acc['email']:<30} {acc['balance']:<10} {status}")
        
        print(f"\n=== 余额统计 ===")
        print(f"正余额账户: {positive_balance}")
        print(f"零余额账户: {zero_balance}")
        print(f"负余额账户: {negative_balance}")
        
        # 计算可用账户（余额 > -1.0）
        available_accounts = [acc for acc in accounts if float(acc['balance'].replace('$', '')) > -1.0]
        print(f"可用账户数 (余额 > -$1.00): {len(available_accounts)}")
        
    else:
        print(f'错误: {response.status_code} - {response.text}')
except Exception as e:
    print(f'异常: {e}')