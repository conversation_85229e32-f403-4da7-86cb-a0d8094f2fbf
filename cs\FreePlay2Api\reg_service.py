import aiohttp
import asyncio
import json


headers = {
    "accept": "application/json",
    "accept-language": "zh-CN,zh;q=0.9",
    "content-type": "application/json",
    "origin": "https://app.freeplay.ai",
    "referer": "https://app.freeplay.ai/signup",
    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36"
}

async def register_user():
    url = "https://app.freeplay.ai/app_data/auth/signup"
    data = {
        "email": "<EMAIL>",
        "password": "qwer1234...",
        "account_name": "asdfvcvx",
        "first_name": "asd",
        "last_name": "ddd"
    }
    
    async with aiohttp.ClientSession() as session:
        async with session.post(url, headers=headers, json=data) as response:
            response_text = await response.text()
            print(response_text)
            print(f"Status: {response.status}")
            return response

# 运行异步函数
if __name__ == "__main__":
    asyncio.run(register_user())