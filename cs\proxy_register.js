// Enhanced FreePlay Registration Service with Free Proxy Pool
// 基于免费代理池的FreePlay账号注册服务

// Configuration
const CONFIG = {
  // Add your auth token here
  AUTH_TOKEN: 'sk-test',
  
  // Proxy settings
  PROXY_SOURCES: [
    'https://www.proxy-list.download/api/v1/get?type=http',
    'https://raw.githubusercontent.com/TheSpeedX/PROXY-List/master/http.txt',
    'https://raw.githubusercontent.com/clarketm/proxy-list/master/proxy-list-raw.txt',
    'https://raw.githubusercontent.com/ShiftyTR/Proxy-List/master/http.txt'
  ],
  
  // Registration settings
  MAX_RETRIES: 5,
  TIMEOUT: 30000,
  CONCURRENT_REGISTRATIONS: 3,
  PROXY_TIMEOUT: 10000,
  
  // Account pool settings
  MIN_ACCOUNTS: 10,
  TARGET_ACCOUNTS: 20,
  BALANCE_THRESHOLD: 2.0
};

// Minimal faker implementation
const faker = {
  random: () => Math.random().toString(36).substring(2, 15),
  firstName: () => {
    const names = ['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
                   '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'];
    return names[<PERSON>.floor(<PERSON>.random() * names.length)];
  },
  last<PERSON>ame: () => {
    const surnames = ['<PERSON>', '<PERSON>', 'Brown', 'Taylor', 'Wilson', 'Davis', 'Miller', 'Garcia', 'Jones', 'Martin',
                      'Anderson', 'Thomas', 'Jackson', 'White', 'Harris', 'Clark', 'Lewis', 'Robinson', 'Walker', 'Hall'];
    return surnames[Math.floor(Math.random() * surnames.length)];
  },
  email: function() {
    const domains = ['gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com', 'protonmail.com', 'icloud.com'];
    const domain = domains[Math.floor(Math.random() * domains.length)];
    return `${this.random()}${Math.floor(Math.random() * 1000)}@${domain}`;
  },
  name: function() {
    return `${this.firstName()} ${this.lastName()}`;
  },
  password: function() {
    const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
    let password = 'aA1!'; // 确保包含必需字符
    for (let i = 0; i < 8; i++) {
      password += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return password;
  }
};

// Proxy Pool Manager
class ProxyPool {
  constructor() {
    this.proxies = [];
    this.workingProxies = [];
    this.failedProxies = new Set();
    this.lastUpdate = 0;
    this.updateInterval = 300000; // 5分钟更新一次
  }

  async fetchProxies() {
    console.log('🔄 Fetching fresh proxy list...');
    const allProxies = new Set();

    for (const source of CONFIG.PROXY_SOURCES) {
      try {
        console.log(`📡 Fetching from: ${source}`);
        const response = await fetch(source, { 
          timeout: 10000,
          headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
          }
        });
        
        if (response.ok) {
          const text = await response.text();
          const proxies = this.parseProxyList(text);
          proxies.forEach(proxy => allProxies.add(proxy));
          console.log(`✅ Found ${proxies.length} proxies from ${source}`);
        }
      } catch (error) {
        console.log(`❌ Failed to fetch from ${source}: ${error.message}`);
      }
    }

    this.proxies = Array.from(allProxies);
    this.lastUpdate = Date.now();
    console.log(`🎯 Total unique proxies collected: ${this.proxies.length}`);
    
    // 测试代理可用性
    await this.testProxies();
  }

  parseProxyList(text) {
    const proxies = [];
    const lines = text.split('\n');
    
    for (const line of lines) {
      const trimmed = line.trim();
      if (trimmed && !trimmed.startsWith('#')) {
        // 支持多种格式: ip:port, http://ip:port
        const match = trimmed.match(/(?:https?:\/\/)?(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}):(\d{1,5})/);
        if (match) {
          const [, ip, port] = match;
          if (this.isValidIP(ip) && parseInt(port) > 0 && parseInt(port) < 65536) {
            proxies.push(`${ip}:${port}`);
          }
        }
      }
    }
    
    return proxies;
  }

  isValidIP(ip) {
    const parts = ip.split('.');
    return parts.length === 4 && parts.every(part => {
      const num = parseInt(part);
      return num >= 0 && num <= 255;
    });
  }

  async testProxies() {
    console.log('🧪 Testing proxy connectivity...');
    const testPromises = [];
    const batchSize = 20; // 批量测试，避免过多并发

    for (let i = 0; i < Math.min(this.proxies.length, 100); i += batchSize) {
      const batch = this.proxies.slice(i, i + batchSize);
      testPromises.push(this.testProxyBatch(batch));
    }

    const results = await Promise.allSettled(testPromises);
    const workingProxies = results.flatMap(result => 
      result.status === 'fulfilled' ? result.value : []
    );

    this.workingProxies = workingProxies;
    console.log(`✅ Working proxies: ${this.workingProxies.length}/${this.proxies.length}`);
  }

  async testProxyBatch(proxies) {
    const working = [];
    const testPromises = proxies.map(proxy => this.testSingleProxy(proxy));
    const results = await Promise.allSettled(testPromises);

    results.forEach((result, index) => {
      if (result.status === 'fulfilled' && result.value) {
        working.push(proxies[index]);
      }
    });

    return working;
  }

  async testSingleProxy(proxy) {
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000);

      const response = await fetch('http://httpbin.org/ip', {
        method: 'GET',
        signal: controller.signal,
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        },
        // Note: In Node.js environment, you'd use a proxy agent here
        // This is a simplified version for demonstration
      });

      clearTimeout(timeoutId);
      return response.ok;
    } catch (error) {
      return false;
    }
  }

  async getWorkingProxy() {
    // 如果代理池为空或过期，重新获取
    if (this.workingProxies.length === 0 || Date.now() - this.lastUpdate > this.updateInterval) {
      await this.fetchProxies();
    }

    if (this.workingProxies.length === 0) {
      throw new Error('No working proxies available');
    }

    // 随机选择一个工作代理
    const randomIndex = Math.floor(Math.random() * this.workingProxies.length);
    const proxy = this.workingProxies[randomIndex];
    
    // 如果代理失败过，移除它
    if (this.failedProxies.has(proxy)) {
      this.workingProxies.splice(randomIndex, 1);
      return this.getWorkingProxy(); // 递归获取下一个
    }

    return proxy;
  }

  markProxyFailed(proxy) {
    this.failedProxies.add(proxy);
    const index = this.workingProxies.indexOf(proxy);
    if (index > -1) {
      this.workingProxies.splice(index, 1);
    }
    console.log(`❌ Marked proxy as failed: ${proxy}`);
  }
}

// Account Manager
class AccountManager {
  constructor() {
    this.accounts = [];
    this.filePath = 'accounts.json';
    this.loadAccounts();
  }

  loadAccounts() {
    try {
      if (require('fs').existsSync(this.filePath)) {
        const data = require('fs').readFileSync(this.filePath, 'utf8');
        this.accounts = data.split('\n')
          .filter(line => line.trim())
          .map(line => JSON.parse(line));
        console.log(`📂 Loaded ${this.accounts.length} existing accounts`);
      }
    } catch (error) {
      console.log(`⚠️ Error loading accounts: ${error.message}`);
      this.accounts = [];
    }
  }

  saveAccounts() {
    try {
      const data = this.accounts.map(account => JSON.stringify(account)).join('\n');
      require('fs').writeFileSync(this.filePath, data, 'utf8');
      console.log(`💾 Saved ${this.accounts.length} accounts to ${this.filePath}`);
    } catch (error) {
      console.log(`❌ Error saving accounts: ${error.message}`);
    }
  }

  addAccount(account) {
    this.accounts.push({
      ...account,
      created_at: new Date().toISOString(),
      status: 'active'
    });
    this.saveAccounts();
    console.log(`✅ Added new account: ${account.email}`);
  }

  getHealthyAccounts() {
    return this.accounts.filter(acc => 
      acc.balance > CONFIG.BALANCE_THRESHOLD && acc.status === 'active'
    );
  }

  needsMoreAccounts() {
    const healthy = this.getHealthyAccounts();
    return healthy.length < CONFIG.MIN_ACCOUNTS;
  }

  getAccountStats() {
    const total = this.accounts.length;
    const healthy = this.getHealthyAccounts().length;
    const totalBalance = this.accounts.reduce((sum, acc) => sum + (acc.balance || 0), 0);
    
    return { total, healthy, totalBalance };
  }
}

// Main registration function with proxy support
async function registerWithProxy(proxy = null) {
  const userData = {
    email: faker.email(),
    password: faker.password(),
    account_name: faker.name(),
    first_name: faker.firstName(),
    last_name: faker.lastName()
  };

  const headers = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    'Accept': 'application/json, text/plain, */*',
    'Accept-Language': 'en-US,en;q=0.9',
    'Accept-Encoding': 'gzip, deflate, br',
    'Content-Type': 'application/json',
    'Origin': 'https://app.freeplay.ai',
    'Referer': 'https://app.freeplay.ai/signup',
    'Sec-Ch-Ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
    'Sec-Ch-Ua-Mobile': '?0',
    'Sec-Ch-Ua-Platform': '"Windows"',
    'Sec-Fetch-Dest': 'empty',
    'Sec-Fetch-Mode': 'cors',
    'Sec-Fetch-Site': 'same-origin'
  };

  const requestOptions = {
    method: 'POST',
    headers: headers,
    body: JSON.stringify(userData),
    timeout: CONFIG.TIMEOUT
  };

  // 如果有代理，添加代理配置
  if (proxy) {
    // Note: In a real Node.js environment, you'd use libraries like 'https-proxy-agent'
    console.log(`🌐 Using proxy: ${proxy}`);
  }

  try {
    console.log(`🚀 Attempting registration for: ${userData.email}`);
    
    const response = await fetch('https://app.freeplay.ai/app_data/auth/signup', requestOptions);
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    
    if (data.project_id) {
      // 从响应头获取session cookie
      const setCookieHeader = response.headers.get('set-cookie');
      let sessionId = null;
      
      if (setCookieHeader) {
        const sessionMatch = setCookieHeader.match(/session=([^;]+)/);
        if (sessionMatch) {
          sessionId = sessionMatch[1];
        }
      }

      const account = {
        email: userData.email,
        password: userData.password,
        session_id: sessionId,
        project_id: data.project_id,
        balance: 5.0, // 新账号默认余额
        proxy_used: proxy
      };

      console.log(`✅ Registration successful: ${userData.email}`);
      return account;
    } else {
      throw new Error('Registration response missing project_id');
    }

  } catch (error) {
    console.log(`❌ Registration failed for ${userData.email}: ${error.message}`);
    if (proxy) {
      throw new Error(`Proxy ${proxy} failed: ${error.message}`);
    }
    throw error;
  }
}

// Main registration orchestrator
async function registerAccounts(count = 1) {
  const proxyPool = new ProxyPool();
  const accountManager = new AccountManager();
  const results = [];
  
  console.log(`🎯 Starting registration of ${count} accounts...`);
  
  // 确保有可用的代理
  await proxyPool.fetchProxies();
  
  if (proxyPool.workingProxies.length === 0) {
    console.log('⚠️ No working proxies found, attempting direct registration...');
  }

  const registrationPromises = [];
  
  for (let i = 0; i < count; i++) {
    const registrationPromise = (async () => {
      let attempts = 0;
      let lastError = null;

      while (attempts < CONFIG.MAX_RETRIES) {
        try {
          let proxy = null;
          
          // 尝试获取代理（如果可用）
          if (proxyPool.workingProxies.length > 0) {
            try {
              proxy = await proxyPool.getWorkingProxy();
            } catch (error) {
              console.log('⚠️ No proxy available, using direct connection');
            }
          }

          const account = await registerWithProxy(proxy);
          accountManager.addAccount(account);
          return { success: true, account };

        } catch (error) {
          lastError = error;
          attempts++;
          
          // 如果是代理错误，标记代理为失败
          if (error.message.includes('Proxy') && error.message.includes('failed')) {
            const failedProxy = error.message.match(/Proxy ([^ ]+) failed/)?.[1];
            if (failedProxy) {
              proxyPool.markProxyFailed(failedProxy);
            }
          }

          if (attempts < CONFIG.MAX_RETRIES) {
            const delay = Math.min(1000 * Math.pow(2, attempts), 10000); // 指数退避
            console.log(`⏳ Retrying in ${delay}ms... (attempt ${attempts + 1}/${CONFIG.MAX_RETRIES})`);
            await new Promise(resolve => setTimeout(resolve, delay));
          }
        }
      }

      return { success: false, error: lastError?.message || 'Unknown error' };
    })();

    registrationPromises.push(registrationPromise);

    // 控制并发数量
    if (registrationPromises.length >= CONFIG.CONCURRENT_REGISTRATIONS) {
      const batchResults = await Promise.allSettled(registrationPromises.splice(0, CONFIG.CONCURRENT_REGISTRATIONS));
      results.push(...batchResults.map(r => r.status === 'fulfilled' ? r.value : { success: false, error: r.reason }));
    }
  }

  // 处理剩余的注册任务
  if (registrationPromises.length > 0) {
    const batchResults = await Promise.allSettled(registrationPromises);
    results.push(...batchResults.map(r => r.status === 'fulfilled' ? r.value : { success: false, error: r.reason }));
  }

  // 统计结果
  const successful = results.filter(r => r.success).length;
  const failed = results.length - successful;

  console.log(`\n📊 Registration Summary:`);
  console.log(`✅ Successful: ${successful}/${count}`);
  console.log(`❌ Failed: ${failed}/${count}`);
  console.log(`🎯 Success Rate: ${((successful / count) * 100).toFixed(1)}%`);

  // 显示账号池状态
  const stats = accountManager.getAccountStats();
  console.log(`\n💰 Account Pool Status:`);
  console.log(`📊 Total Accounts: ${stats.total}`);
  console.log(`💚 Healthy Accounts: ${stats.healthy}`);
  console.log(`💵 Total Balance: $${stats.totalBalance.toFixed(2)}`);

  return results;
}

// Auto-maintenance function
async function autoMaintenance() {
  const accountManager = new AccountManager();
  
  console.log('🔧 Starting auto-maintenance...');
  
  if (accountManager.needsMoreAccounts()) {
    const needed = CONFIG.TARGET_ACCOUNTS - accountManager.getHealthyAccounts().length;
    console.log(`📈 Need ${needed} more accounts, starting registration...`);
    
    await registerAccounts(needed);
  } else {
    console.log('✅ Account pool is healthy, no registration needed');
  }
}

// Export functions for use in other modules
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    registerAccounts,
    autoMaintenance,
    ProxyPool,
    AccountManager,
    CONFIG
  };
}

// CLI interface
if (require.main === module) {
  const args = process.argv.slice(2);
  const command = args[0];
  const count = parseInt(args[1]) || 1;

  switch (command) {
    case 'register':
      registerAccounts(count).catch(console.error);
      break;
    case 'maintain':
      autoMaintenance().catch(console.error);
      break;
    case 'status':
      const accountManager = new AccountManager();
      const stats = accountManager.getAccountStats();
      console.log('📊 Account Pool Status:');
      console.log(`Total: ${stats.total}, Healthy: ${stats.healthy}, Balance: $${stats.totalBalance.toFixed(2)}`);
      break;
    default:
      console.log('Usage:');
      console.log('  node proxy_register.js register [count] - Register new accounts');
      console.log('  node proxy_register.js maintain - Auto-maintain account pool');
      console.log('  node proxy_register.js status - Show account pool status');
  }
}